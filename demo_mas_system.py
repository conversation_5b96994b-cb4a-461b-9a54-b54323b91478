#!/usr/bin/env python3
"""
CAD知识图谱多智能体系统演示脚本
展示系统的核心功能和使用方法
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

try:
    from src.agent import MultiAgentSystem, MultimodalQuery, RetrievalStrategy, QueryType
    import numpy as np
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所有依赖包并正确配置了Python路径")
    sys.exit(1)


class CADQueryDemo:
    """CAD查询演示类"""
    
    def __init__(self):
        self.mas = None
        
    async def initialize(self):
        """初始化多智能体系统"""
        print("🚀 正在初始化CAD知识图谱多智能体系统...")
        try:
            self.mas = MultiAgentSystem()
            await self.mas.start()
            print("✅ 系统初始化成功!")
            return True
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.mas:
            await self.mas.stop()
            print("🔄 系统已关闭")
    
    async def demo_basic_query(self):
        """演示基本文本查询"""
        print("\n" + "="*60)
        print("📝 演示1: 基本文本查询")
        print("="*60)
        
        queries = [
            "查找所有装配体",
            "寻找发动机相关的零件",
            "查找包含螺栓的装配体"
        ]
        
        for i, query_text in enumerate(queries, 1):
            print(f"\n🔍 查询 {i}: {query_text}")
            
            try:
                result = await self.mas.process_user_query(query_text)
                
                if result['success']:
                    print(f"✅ 查询成功")
                    print(f"   执行时间: {result.get('metadata', {}).get('execution_time', 0):.2f}秒")
                    print(f"   结果数量: {len(result.get('results', []))}")
                    
                    # 显示前3个结果
                    for j, res in enumerate(result.get('results', [])[:3]):
                        data = res.get('data', {})
                        print(f"   结果{j+1}: {data.get('name', 'N/A')} "
                              f"(类型: {res.get('type', 'N/A')}, "
                              f"置信度: {res.get('confidence_score', 0):.2f})")
                else:
                    print(f"❌ 查询失败: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                print(f"❌ 查询异常: {e}")
    
    async def demo_multimodal_query(self):
        """演示多模态查询"""
        print("\n" + "="*60)
        print("🎯 演示2: 多模态查询")
        print("="*60)
        
        # 创建多模态查询
        query = MultimodalQuery(
            text_query="查找铝合金材料的机械零件",
            filters={
                "material": "aluminum",
                "category": "mechanical"
            },
            query_intent="寻找特定材料和类别的零件"
        )
        
        print(f"🔍 多模态查询:")
        print(f"   文本: {query.text_query}")
        print(f"   过滤器: {query.filters}")
        print(f"   意图: {query.query_intent}")
        
        try:
            result = await self.mas.process_multimodal_query_advanced(query)
            
            if result['success']:
                print(f"✅ 查询成功")
                print(f"   查询ID: {result['query_id']}")
                print(f"   执行时间: {result['execution_time']:.2f}秒")
                print(f"   结果数量: {len(result.get('results', []))}")
                
                # 显示结果详情
                for i, res in enumerate(result.get('results', [])[:3]):
                    data = res.get('data', {})
                    print(f"   结果{i+1}:")
                    print(f"     名称: {data.get('name', 'N/A')}")
                    print(f"     材料: {data.get('material', 'N/A')}")
                    print(f"     质量: {data.get('mass', 'N/A')}")
                    print(f"     置信度: {res.get('confidence_score', 0):.2f}")
            else:
                print(f"❌ 查询失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
    
    async def demo_custom_strategy(self):
        """演示自定义检索策略"""
        print("\n" + "="*60)
        print("⚙️ 演示3: 自定义检索策略")
        print("="*60)
        
        # 创建自定义策略
        strategy = RetrievalStrategy(
            query_types=[QueryType.METADATA, QueryType.STRUCTURE],
            parallel_execution=True,
            fusion_required=True,
            confidence_threshold=0.5,
            max_results_per_query=15
        )
        
        print(f"🎛️ 自定义策略配置:")
        print(f"   查询类型: {[qt.value for qt in strategy.query_types]}")
        print(f"   并行执行: {strategy.parallel_execution}")
        print(f"   需要融合: {strategy.fusion_required}")
        print(f"   置信度阈值: {strategy.confidence_threshold}")
        print(f"   最大结果数: {strategy.max_results_per_query}")
        
        query = MultimodalQuery(
            text_query="查找包含孔特征的零件",
            filters={"type": "Part"}
        )
        
        try:
            result = await self.mas.process_multimodal_query_advanced(query, strategy)
            
            if result['success']:
                print(f"✅ 自定义策略查询成功")
                print(f"   执行时间: {result['execution_time']:.2f}秒")
                print(f"   结果数量: {len(result.get('results', []))}")
            else:
                print(f"❌ 查询失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
    
    async def demo_parallel_queries(self):
        """演示并行查询"""
        print("\n" + "="*60)
        print("🚀 演示4: 并行查询处理")
        print("="*60)
        
        # 创建多个查询
        queries = [
            MultimodalQuery(text_query="查找装配体", filters={"category": "automotive"}),
            MultimodalQuery(text_query="查找钢材零件", filters={"material": "steel"}),
            MultimodalQuery(text_query="查找圆孔特征", filters={"type": "hole"})
        ]
        
        print(f"📋 准备并行执行 {len(queries)} 个查询:")
        for i, q in enumerate(queries, 1):
            print(f"   查询{i}: {q.text_query}")
        
        try:
            start_time = datetime.now()
            results = await self.mas.execute_parallel_queries(queries, max_concurrent=2)
            total_time = (datetime.now() - start_time).total_seconds()
            
            print(f"✅ 并行查询完成，总耗时: {total_time:.2f}秒")
            
            for i, result in enumerate(results, 1):
                if result['success']:
                    print(f"   查询{i}: 成功 ({result.get('execution_time', 0):.2f}秒, "
                          f"{len(result.get('results', []))}个结果)")
                else:
                    print(f"   查询{i}: 失败 - {result.get('error', '未知错误')}")
                    
        except Exception as e:
            print(f"❌ 并行查询异常: {e}")
    
    async def demo_system_monitoring(self):
        """演示系统监控"""
        print("\n" + "="*60)
        print("📊 演示5: 系统状态监控")
        print("="*60)
        
        # 获取系统状态
        status = self.mas.get_system_status()
        
        print(f"🖥️ 系统状态:")
        print(f"   运行状态: {'🟢 运行中' if status['is_running'] else '🔴 已停止'}")
        print(f"   活跃会话数: {status['active_sessions']}")
        
        print(f"\n🤖 智能体状态:")
        for agent_id, agent_status in status['agents'].items():
            print(f"   {agent_status['name']} ({agent_id}):")
            print(f"     状态: {agent_status['status']}")
            print(f"     消息数量: {agent_status['message_count']}")
        
        # 执行一个查询来创建会话
        print(f"\n🔄 执行测试查询创建会话...")
        await self.mas.process_user_query("系统状态测试查询", session_id="demo_session")
        
        # 检查更新后的状态
        updated_status = self.mas.get_system_status()
        print(f"   更新后的活跃会话数: {updated_status['active_sessions']}")
        
        # 获取会话详情
        session_info = self.mas.get_session_info("demo_session")
        if session_info:
            print(f"   会话详情:")
            print(f"     会话ID: {session_info['session_id']}")
            print(f"     状态: {session_info['status']}")
            print(f"     开始时间: {session_info['start_time']}")
    
    async def run_all_demos(self):
        """运行所有演示"""
        print("🎪 CAD知识图谱多智能体系统功能演示")
        print("="*60)
        
        if not await self.initialize():
            return
        
        try:
            demos = [
                self.demo_basic_query,
                self.demo_multimodal_query,
                self.demo_custom_strategy,
                self.demo_parallel_queries,
                self.demo_system_monitoring
            ]
            
            for demo in demos:
                try:
                    await demo()
                    await asyncio.sleep(1)  # 短暂暂停
                except Exception as e:
                    print(f"❌ 演示执行失败: {e}")
                    import traceback
                    traceback.print_exc()
            
            print("\n" + "="*60)
            print("🎉 所有演示完成!")
            print("="*60)
            
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    demo = CADQueryDemo()
    await demo.run_all_demos()


if __name__ == "__main__":
    print("启动CAD知识图谱多智能体系统演示...")
    print("请确保Neo4j数据库正在运行并包含CAD数据")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n\n💥 演示执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 感谢使用CAD知识图谱多智能体系统!")
