"""
多模态查询智能体 (Multi-modal Query Agent)
负责与Neo4j图数据库的所有交互，提供统一的数据访问接口
"""

import asyncio
import json
import numpy as np
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import logging

from .base_agent import (
    BaseAgent, AgentMessage, QueryResult, MessageType, QueryType,
    MultimodalQuery
)
from ..knowledge_graph import CADKnowledgeGraph
from ..shape_description.clip import CLIPShapeDescriptor
from scripts.shape_embedding_index import ShapeEmbeddingIndex


class MultimodalQueryAgent(BaseAgent):
    """多模态查询智能体 - 封装Neo4j数据库交互"""
    
    def __init__(self, agent_id: str = "multimodal_query", name: str = "多模态查询智能体"):
        super().__init__(agent_id, name)
        
        # 初始化数据库连接
        self.kg = CADKnowledgeGraph.from_config()
        
        # 初始化形状描述器和向量索引
        self.shape_descriptor = CLIPShapeDescriptor()
        self.shape_index = ShapeEmbeddingIndex(self.kg)
        
        # 查询执行器映射
        self.query_executors = {
            QueryType.METADATA: self._execute_metadata_query,
            QueryType.STRUCTURE: self._execute_structure_query,
            QueryType.VECTOR_SIMILARITY: self._execute_vector_similarity_query,
            QueryType.HYBRID: self._execute_hybrid_query
        }
        
        logging.info(f"多模态查询智能体 {self.agent_id} 初始化完成")
    
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理接收到的消息"""
        self.log_message(message)
        
        if message.message_type == MessageType.COMMAND:
            # 处理查询命令
            return await self._handle_query_command(message)
        elif message.message_type == MessageType.QUERY:
            # 处理直接查询
            return await self._handle_direct_query(message)
        else:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"不支持的消息类型: {message.message_type}"}
            )
    
    async def execute_task(self, task: Dict[str, Any]) -> QueryResult:
        """执行查询任务"""
        query_id = task.get("query_id", "")
        query_type = QueryType(task.get("query_type", "metadata"))
        query_params = task.get("query_params", {})
        
        start_time = datetime.now()
        
        try:
            # 根据查询类型选择执行器
            executor = self.query_executors.get(query_type)
            if not executor:
                raise ValueError(f"不支持的查询类型: {query_type}")
            
            # 执行查询
            results = await executor(query_params)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return QueryResult(
                query_id=query_id,
                query_type=query_type,
                results=results,
                execution_time=execution_time,
                metadata={
                    "query_params": query_params,
                    "result_count": len(results)
                }
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logging.error(f"查询执行失败: {e}")
            
            return QueryResult(
                query_id=query_id,
                query_type=query_type,
                execution_time=execution_time,
                error=str(e)
            )
    
    async def _handle_query_command(self, message: AgentMessage) -> AgentMessage:
        """处理查询命令"""
        try:
            command_data = message.content
            task = {
                "query_id": command_data.get("query_id", ""),
                "query_type": command_data.get("query_type", "metadata"),
                "query_params": command_data.get("query_params", {})
            }
            
            result = await self.execute_task(task)
            
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.RESPONSE,
                content={
                    "query_id": result.query_id,
                    "success": result.is_successful(),
                    "results": result.results,
                    "metadata": result.metadata,
                    "error": result.error
                }
            )
            
        except Exception as e:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"处理查询命令失败: {str(e)}"}
            )
    
    async def _handle_direct_query(self, message: AgentMessage) -> AgentMessage:
        """处理直接查询"""
        try:
            multimodal_query = MultimodalQuery(**message.content.get("query", {}))
            
            # 根据查询内容自动确定查询类型
            query_type = self._determine_query_type(multimodal_query)
            
            # 构建查询参数
            query_params = self._build_query_params(multimodal_query)
            
            task = {
                "query_id": multimodal_query.query_id,
                "query_type": query_type.value,
                "query_params": query_params
            }
            
            result = await self.execute_task(task)
            
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.RESPONSE,
                content={
                    "query_id": result.query_id,
                    "success": result.is_successful(),
                    "results": result.results,
                    "metadata": result.metadata,
                    "error": result.error
                }
            )
            
        except Exception as e:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"处理直接查询失败: {str(e)}"}
            )
    
    def _determine_query_type(self, multimodal_query: MultimodalQuery) -> QueryType:
        """根据查询内容自动确定查询类型"""
        
        modality_count = multimodal_query.get_modality_count()
        
        # 如果有图像或形状数据，优先使用向量相似性搜索
        if multimodal_query.has_image() or multimodal_query.has_sketch():
            return QueryType.VECTOR_SIMILARITY
        
        # 如果有文本查询且包含结构关键词，使用结构查询
        if multimodal_query.has_text():
            text = multimodal_query.text_query.lower()
            structure_keywords = ["包含", "组成", "结构", "关系", "子装配", "零件", "特征"]
            if any(keyword in text for keyword in structure_keywords):
                return QueryType.STRUCTURE
        
        # 如果有多种模态，使用混合查询
        if modality_count > 1:
            return QueryType.HYBRID
        
        # 默认使用元数据查询
        return QueryType.METADATA
    
    def _build_query_params(self, multimodal_query: MultimodalQuery) -> Dict[str, Any]:
        """构建查询参数"""
        params = {
            "filters": multimodal_query.filters,
            "text_query": multimodal_query.text_query,
            "query_intent": multimodal_query.query_intent
        }
        
        # 如果有图像数据，提取特征向量
        if multimodal_query.has_image():
            try:
                # 这里应该处理图像数据并提取特征向量
                # 为了简化，假设image_data是已经处理好的向量
                params["image_embedding"] = multimodal_query.image_data
            except Exception as e:
                logging.warning(f"图像特征提取失败: {e}")
        
        return params
    
    async def _execute_metadata_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行元数据查询"""
        
        filters = query_params.get("filters", {})
        text_query = query_params.get("text_query", "")
        
        results = []
        
        try:
            # 基于属性过滤的查询
            if filters:
                for property_name, property_value in filters.items():
                    entities = self.kg.search_by_property(property_name, property_value)
                    results.extend(entities)
            
            # 基于文本的模糊搜索
            if text_query:
                text_results = await self._execute_text_search(text_query)
                results.extend(text_results)
            
            # 去重并格式化结果
            unique_results = self._deduplicate_results(results)
            return unique_results
            
        except Exception as e:
            logging.error(f"元数据查询执行失败: {e}")
            raise
    
    async def _execute_structure_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行图结构查询"""
        
        text_query = query_params.get("text_query", "")
        filters = query_params.get("filters", {})
        
        try:
            # 解析结构查询意图
            structure_intent = self._parse_structure_intent(text_query)
            
            if structure_intent["type"] == "assembly_structure":
                # 查询装配体结构
                return await self._query_assembly_structure(structure_intent, filters)
            elif structure_intent["type"] == "part_relationships":
                # 查询零件关系
                return await self._query_part_relationships(structure_intent, filters)
            elif structure_intent["type"] == "feature_search":
                # 查询特征
                return await self._query_features(structure_intent, filters)
            else:
                # 通用结构查询
                return await self._query_general_structure(structure_intent, filters)
                
        except Exception as e:
            logging.error(f"结构查询执行失败: {e}")
            raise
    
    async def _execute_vector_similarity_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行向量相似性查询"""
        
        image_embedding = query_params.get("image_embedding")
        filters = query_params.get("filters", {})
        top_k = query_params.get("top_k", 10)
        similarity_threshold = query_params.get("similarity_threshold", 0.6)
        
        try:
            if image_embedding is None:
                raise ValueError("向量相似性查询需要图像嵌入向量")
            
            # 确保向量格式正确
            if isinstance(image_embedding, list):
                image_embedding = np.array(image_embedding)
            
            # 使用形状索引进行相似性搜索
            similar_nodes = self.shape_index.search_similar_nodes(
                query_vector=image_embedding,
                top_k=top_k,
                similarity_threshold=similarity_threshold
            )
            
            # 格式化结果
            results = []
            for node in similar_nodes:
                result = {
                    "id": node.get("id"),
                    "type": node.get("type", "unknown"),
                    "name": node.get("name"),
                    "similarity_score": node.get("score", 0.0),
                    "properties": node.get("properties", {})
                }
                results.append(result)
            
            # 应用额外过滤器
            if filters:
                results = self._apply_filters(results, filters)
            
            return results
            
        except Exception as e:
            logging.error(f"向量相似性查询执行失败: {e}")
            raise
    
    async def _execute_hybrid_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行混合查询"""
        
        try:
            # 并行执行多种查询类型
            tasks = []
            
            # 元数据查询
            if query_params.get("text_query") or query_params.get("filters"):
                tasks.append(self._execute_metadata_query(query_params))
            
            # 向量相似性查询
            if query_params.get("image_embedding") is not None:
                tasks.append(self._execute_vector_similarity_query(query_params))
            
            # 结构查询
            if query_params.get("text_query"):
                tasks.append(self._execute_structure_query(query_params))
            
            # 等待所有查询完成
            if tasks:
                results_list = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 合并结果
                all_results = []
                for results in results_list:
                    if isinstance(results, list):
                        all_results.extend(results)
                
                # 去重并排序
                unique_results = self._deduplicate_results(all_results)
                return self._rank_hybrid_results(unique_results)
            
            return []
            
        except Exception as e:
            logging.error(f"混合查询执行失败: {e}")
            raise
    
    async def _execute_text_search(self, text_query: str) -> List[Dict[str, Any]]:
        """执行文本搜索"""
        
        # 简单的文本匹配查询
        query = """
        MATCH (n)
        WHERE toLower(n.name) CONTAINS toLower($text)
           OR toLower(n.material) CONTAINS toLower($text)
           OR toLower(n.category) CONTAINS toLower($text)
           OR toLower(n.industry) CONTAINS toLower($text)
        RETURN n, labels(n) as types
        LIMIT 50
        """
        
        params = {"text": text_query}
        
        with self.kg.driver.session() as session:
            result = session.run(query, params)
            return [{"entity": dict(record["n"]), "types": record["types"]} 
                   for record in result]
    
    def _parse_structure_intent(self, text_query: str) -> Dict[str, Any]:
        """解析结构查询意图"""
        
        text = text_query.lower()
        
        if any(keyword in text for keyword in ["装配体", "assembly", "结构"]):
            return {"type": "assembly_structure", "target": "assembly"}
        elif any(keyword in text for keyword in ["零件", "part", "组件"]):
            return {"type": "part_relationships", "target": "part"}
        elif any(keyword in text for keyword in ["特征", "feature", "孔", "槽"]):
            return {"type": "feature_search", "target": "feature"}
        else:
            return {"type": "general_structure", "target": "all"}
    
    async def _query_assembly_structure(self, intent: Dict[str, Any], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询装配体结构"""
        
        query = """
        MATCH (a:Assembly)
        OPTIONAL MATCH (a)-[:hasSubAssembly]->(sub:SubAssembly)
        OPTIONAL MATCH (a)-[:hasPart]->(p:Part)
        OPTIONAL MATCH (p)-[:hasFeature]->(f:Feature)
        RETURN a, collect(distinct sub) as subassemblies, 
               collect(distinct p) as parts, collect(distinct f) as features
        LIMIT 20
        """
        
        with self.kg.driver.session() as session:
            result = session.run(query)
            structures = []
            for record in result:
                structure = {
                    "assembly": dict(record["a"]),
                    "subassemblies": [dict(sub) for sub in record["subassemblies"]],
                    "parts": [dict(part) for part in record["parts"]],
                    "features": [dict(feature) for feature in record["features"]]
                }
                structures.append(structure)
            return structures
    
    async def _query_part_relationships(self, intent: Dict[str, Any], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询零件关系"""
        
        query = """
        MATCH (p:Part)
        OPTIONAL MATCH (parent)-[:hasPart]->(p)
        OPTIONAL MATCH (p)-[:hasFeature]->(f:Feature)
        RETURN p, parent, collect(f) as features
        LIMIT 50
        """
        
        with self.kg.driver.session() as session:
            result = session.run(query)
            relationships = []
            for record in result:
                relationship = {
                    "part": dict(record["p"]),
                    "parent": dict(record["parent"]) if record["parent"] else None,
                    "features": [dict(feature) for feature in record["features"]]
                }
                relationships.append(relationship)
            return relationships
    
    async def _query_features(self, intent: Dict[str, Any], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询特征"""
        
        query = """
        MATCH (f:Feature)
        OPTIONAL MATCH (p:Part)-[:hasFeature]->(f)
        RETURN f, p
        LIMIT 100
        """
        
        with self.kg.driver.session() as session:
            result = session.run(query)
            features = []
            for record in result:
                feature = {
                    "feature": dict(record["f"]),
                    "part": dict(record["p"]) if record["p"] else None
                }
                features.append(feature)
            return features
    
    async def _query_general_structure(self, intent: Dict[str, Any], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """通用结构查询"""
        
        query = """
        MATCH (n)-[r]->(m)
        RETURN n, type(r) as relationship, m
        LIMIT 100
        """
        
        with self.kg.driver.session() as session:
            result = session.run(query)
            structures = []
            for record in result:
                structure = {
                    "source": dict(record["n"]),
                    "relationship": record["relationship"],
                    "target": dict(record["m"])
                }
                structures.append(structure)
            return structures
    
    def _apply_filters(self, results: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用过滤器"""
        
        filtered_results = []
        for result in results:
            match = True
            for key, value in filters.items():
                if key in result and result[key] != value:
                    match = False
                    break
            if match:
                filtered_results.append(result)
        
        return filtered_results
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重结果"""
        
        seen_ids = set()
        unique_results = []
        
        for result in results:
            # 尝试获取唯一标识符
            entity_id = None
            if "entity" in result and "id" in result["entity"]:
                entity_id = result["entity"]["id"]
            elif "id" in result:
                entity_id = result["id"]
            elif "assembly" in result and "id" in result["assembly"]:
                entity_id = result["assembly"]["id"]
            
            if entity_id and entity_id not in seen_ids:
                seen_ids.add(entity_id)
                unique_results.append(result)
            elif entity_id is None:
                # 如果没有ID，直接添加
                unique_results.append(result)
        
        return unique_results
    
    def _rank_hybrid_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """对混合查询结果进行排序"""
        
        # 简单的排序策略：优先考虑相似度分数
        def get_score(result):
            if "similarity_score" in result:
                return result["similarity_score"]
            return 0.5  # 默认分数
        
        return sorted(results, key=get_score, reverse=True)
    
    def close(self):
        """关闭数据库连接"""
        if self.kg:
            self.kg.close()
        logging.info(f"多模态查询智能体 {self.agent_id} 已关闭")
