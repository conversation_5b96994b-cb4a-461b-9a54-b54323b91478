"""
多模态查询智能体 (Multi-modal Query Agent)
负责与Neo4j图数据库的所有交互，提供统一的数据访问接口
"""

import asyncio
import json
import numpy as np
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import logging

from .base_agent import (
    BaseAgent, AgentMessage, QueryResult, MessageType, QueryType,
    MultimodalQuery
)
from ..knowledge_graph import CADKnowledgeGraph
from ..shape_description.clip import CLIPShapeDescriptor
from scripts.shape_embedding_index import ShapeEmbeddingIndex


class MultimodalQueryAgent(BaseAgent):
    """多模态查询智能体 - 封装Neo4j数据库交互"""
    
    def __init__(self, agent_id: str = "multimodal_query", name: str = "多模态查询智能体"):
        super().__init__(agent_id, name)
        
        # 初始化数据库连接
        self.kg = CADKnowledgeGraph.from_config()
        
        # 初始化形状描述器和向量索引
        self.shape_descriptor = CLIPShapeDescriptor()
        self.shape_index = ShapeEmbeddingIndex(self.kg)
        
        # 查询执行器映射
        self.query_executors = {
            QueryType.METADATA: self._execute_metadata_query,
            QueryType.STRUCTURE: self._execute_structure_query,
            QueryType.VECTOR_SIMILARITY: self._execute_vector_similarity_query,
            QueryType.HYBRID: self._execute_hybrid_query
        }
        
        logging.info(f"多模态查询智能体 {self.agent_id} 初始化完成")
    
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理接收到的消息"""
        self.log_message(message)
        
        if message.message_type == MessageType.COMMAND:
            # 处理查询命令
            return await self._handle_query_command(message)
        elif message.message_type == MessageType.QUERY:
            # 处理直接查询
            return await self._handle_direct_query(message)
        else:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"不支持的消息类型: {message.message_type}"}
            )
    
    async def execute_task(self, task: Dict[str, Any]) -> QueryResult:
        """执行查询任务"""
        query_id = task.get("query_id", "")
        query_type = QueryType(task.get("query_type", "metadata"))
        query_params = task.get("query_params", {})
        
        start_time = datetime.now()
        
        try:
            # 根据查询类型选择执行器
            executor = self.query_executors.get(query_type)
            if not executor:
                raise ValueError(f"不支持的查询类型: {query_type}")
            
            # 执行查询
            results = await executor(query_params)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return QueryResult(
                query_id=query_id,
                query_type=query_type,
                results=results,
                execution_time=execution_time,
                metadata={
                    "query_params": query_params,
                    "result_count": len(results)
                }
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logging.error(f"查询执行失败: {e}")
            
            return QueryResult(
                query_id=query_id,
                query_type=query_type,
                execution_time=execution_time,
                error=str(e)
            )
    
    async def _handle_query_command(self, message: AgentMessage) -> AgentMessage:
        """处理查询命令"""
        try:
            command_data = message.content
            task = {
                "query_id": command_data.get("query_id", ""),
                "query_type": command_data.get("query_type", "metadata"),
                "query_params": command_data.get("query_params", {})
            }
            
            result = await self.execute_task(task)
            
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.RESPONSE,
                content={
                    "query_id": result.query_id,
                    "success": result.is_successful(),
                    "results": result.results,
                    "metadata": result.metadata,
                    "error": result.error
                }
            )
            
        except Exception as e:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"处理查询命令失败: {str(e)}"}
            )
    
    async def _handle_direct_query(self, message: AgentMessage) -> AgentMessage:
        """处理直接查询"""
        try:
            multimodal_query = MultimodalQuery(**message.content.get("query", {}))
            
            # 根据查询内容自动确定查询类型
            query_type = self._determine_query_type(multimodal_query)
            
            # 构建查询参数
            query_params = self._build_query_params(multimodal_query)
            
            task = {
                "query_id": multimodal_query.query_id,
                "query_type": query_type.value,
                "query_params": query_params
            }
            
            result = await self.execute_task(task)
            
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.RESPONSE,
                content={
                    "query_id": result.query_id,
                    "success": result.is_successful(),
                    "results": result.results,
                    "metadata": result.metadata,
                    "error": result.error
                }
            )
            
        except Exception as e:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"处理直接查询失败: {str(e)}"}
            )
    
    def _determine_query_type(self, multimodal_query: MultimodalQuery) -> QueryType:
        """根据查询内容自动确定查询类型"""
        
        modality_count = multimodal_query.get_modality_count()
        
        # 如果有图像或形状数据，优先使用向量相似性搜索
        if multimodal_query.has_image() or multimodal_query.has_sketch():
            return QueryType.VECTOR_SIMILARITY
        
        # 如果有文本查询且包含结构关键词，使用结构查询
        if multimodal_query.has_text():
            text = multimodal_query.text_query.lower()
            structure_keywords = ["包含", "组成", "结构", "关系", "子装配", "零件", "特征"]
            if any(keyword in text for keyword in structure_keywords):
                return QueryType.STRUCTURE
        
        # 如果有多种模态，使用混合查询
        if modality_count > 1:
            return QueryType.HYBRID
        
        # 默认使用元数据查询
        return QueryType.METADATA
    
    def _build_query_params(self, multimodal_query: MultimodalQuery) -> Dict[str, Any]:
        """构建查询参数"""
        params = {
            "filters": multimodal_query.filters,
            "text_query": multimodal_query.text_query,
            "query_intent": multimodal_query.query_intent
        }

        # 如果有图像数据，提取特征向量
        if multimodal_query.has_image():
            try:
                # 这里应该处理图像数据并提取特征向量
                # 为了简化，假设image_data是已经处理好的向量
                params["image_embedding"] = multimodal_query.image_data
                params["embedding_type"] = "shape_embedding"
            except Exception as e:
                logging.warning(f"图像特征提取失败: {e}")

        # 如果只有文本查询，默认使用描述嵌入
        elif multimodal_query.has_text():
            params["embedding_type"] = "description_embedding"

        return params

    def _build_enhanced_query_params(self,
                                   multimodal_query: MultimodalQuery,
                                   decomposition_result: Dict[str, Any]) -> Dict[str, Any]:
        """构建增强的查询参数，包含分解结果"""

        base_params = self._build_query_params(multimodal_query)

        # 添加分解结果
        base_params.update({
            "sub_queries": decomposition_result.get("sub_queries", []),
            "execution_strategy": decomposition_result.get("execution_strategy", {}),
            "query_intent": decomposition_result.get("intent", multimodal_query.query_intent),
            "input_modality": decomposition_result.get("input_modality", "文本")
        })

        return base_params
    
    async def _execute_metadata_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行元数据查询"""

        filters = query_params.get("filters", {})
        text_query = query_params.get("text_query", "")
        required_attributes = query_params.get("required_attributes", [])
        target_entities = query_params.get("target_entities", ["Assembly", "Part"])
        previous_results = query_params.get("previous_results", [])

        results = []

        try:
            # 1. 基于属性过滤的精确查询
            if filters:
                for property_name, property_value in filters.items():
                    entities = self.kg.search_by_property(property_name, property_value)
                    results.extend(entities)

            # 2. 基于文本的智能属性提取和查询
            if text_query:
                extracted_queries = self._extract_metadata_from_text(text_query, required_attributes)
                for extracted_query in extracted_queries:
                    text_results = await self._execute_extracted_metadata_query(
                        extracted_query, target_entities, previous_results
                    )
                    results.extend(text_results)

            # 3. 如果有前序查询结果，进行过滤
            if previous_results:
                results = self._filter_by_previous_results(results, previous_results)

            # 去重并格式化结果
            unique_results = self._deduplicate_results(results)
            return unique_results

        except Exception as e:
            logging.error(f"元数据查询执行失败: {e}")
            raise

    def _extract_metadata_from_text(self, text_query: str, required_attributes: List[str]) -> List[Dict[str, Any]]:
        """从文本中提取元数据查询条件"""

        extracted_queries = []
        text = text_query.lower()

        # 材料提取
        material_patterns = {
            "aluminum": ["铝", "铝合金", "aluminum"],
            "steel": ["钢", "钢材", "steel", "不锈钢"],
            "plastic": ["塑料", "plastic", "聚合物"],
            "titanium": ["钛", "钛合金", "titanium"],
            "carbon": ["碳纤维", "carbon"]
        }

        for material, keywords in material_patterns.items():
            if any(keyword in text for keyword in keywords):
                extracted_queries.append({
                    "attribute": "material",
                    "value": material,
                    "match_type": "exact"
                })

        # 类别提取
        category_patterns = {
            "mechanical": ["机械", "mechanical"],
            "automotive": ["汽车", "automotive", "车辆"],
            "aerospace": ["航空", "aerospace", "飞机"],
            "electronic": ["电子", "electronic", "电气"]
        }

        for category, keywords in category_patterns.items():
            if any(keyword in text for keyword in keywords):
                extracted_queries.append({
                    "attribute": "category",
                    "value": category,
                    "match_type": "exact"
                })

        # 名称模糊匹配
        name_keywords = ["发动机", "轮子", "螺栓", "齿轮", "轴承", "外壳"]
        for keyword in name_keywords:
            if keyword in text:
                extracted_queries.append({
                    "attribute": "name",
                    "value": keyword,
                    "match_type": "contains"
                })

        return extracted_queries

    async def _execute_extracted_metadata_query(self,
                                              extracted_query: Dict[str, Any],
                                              target_entities: List[str],
                                              previous_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行提取的元数据查询"""

        attribute = extracted_query["attribute"]
        value = extracted_query["value"]
        match_type = extracted_query["match_type"]

        # 构建节点标签过滤
        entity_labels = " OR ".join([f"n:{entity}" for entity in target_entities])

        # 构建属性匹配条件
        if match_type == "exact":
            condition = f"n.{attribute} = $value"
        elif match_type == "contains":
            condition = f"toLower(n.{attribute}) CONTAINS toLower($value)"
        else:
            condition = f"n.{attribute} = $value"

        # 构建前序结果过滤条件
        previous_filter = ""
        if previous_results:
            previous_ids = [res.get("entity", {}).get("id") for res in previous_results
                          if res.get("entity", {}).get("id")]
            if previous_ids:
                previous_filter = f"AND n.id IN {previous_ids}"

        query = f"""
        MATCH (n)
        WHERE ({entity_labels}) AND {condition} {previous_filter}
        RETURN n, labels(n) as types
        LIMIT 50
        """

        params = {"value": value}

        with self.kg.driver.session() as session:
            result = session.run(query, params)
            return [{"entity": dict(record["n"]), "types": record["types"]}
                   for record in result]
    
    async def _execute_structure_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行图结构查询"""
        
        text_query = query_params.get("text_query", "")
        filters = query_params.get("filters", {})
        
        try:
            # 解析结构查询意图
            structure_intent = self._parse_structure_intent(text_query)
            
            if structure_intent["type"] == "assembly_structure":
                # 查询装配体结构
                return await self._query_assembly_structure(structure_intent, filters)
            elif structure_intent["type"] == "part_relationships":
                # 查询零件关系
                return await self._query_part_relationships(structure_intent, filters)
            elif structure_intent["type"] == "feature_search":
                # 查询特征
                return await self._query_features(structure_intent, filters)
            else:
                # 通用结构查询
                return await self._query_general_structure(structure_intent, filters)
                
        except Exception as e:
            logging.error(f"结构查询执行失败: {e}")
            raise
    
    async def _execute_vector_similarity_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行向量相似性查询"""

        # 获取查询参数
        image_embedding = query_params.get("image_embedding")
        text_query = query_params.get("text_query", "")
        embedding_type = query_params.get("embedding_type", "shape_embedding")
        target_entities = query_params.get("target_entities", ["Assembly", "Part"])
        filters = query_params.get("filters", {})
        top_k = query_params.get("top_k", 10)
        similarity_threshold = query_params.get("similarity_threshold", 0.6)
        previous_results = query_params.get("previous_results", [])

        try:
            # 根据嵌入类型选择不同的处理方式
            if embedding_type == "shape_embedding":
                return await self._execute_shape_similarity_query(
                    image_embedding, target_entities, filters, top_k,
                    similarity_threshold, previous_results
                )
            elif embedding_type == "description_embedding":
                return await self._execute_description_similarity_query(
                    text_query, target_entities, filters, top_k,
                    similarity_threshold, previous_results
                )
            else:
                raise ValueError(f"不支持的嵌入类型: {embedding_type}")

        except Exception as e:
            logging.error(f"向量相似性查询执行失败: {e}")
            raise

    async def _execute_shape_similarity_query(self,
                                            image_embedding: np.ndarray,
                                            target_entities: List[str],
                                            filters: Dict[str, Any],
                                            top_k: int,
                                            similarity_threshold: float,
                                            previous_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行基于形状嵌入的相似性查询"""

        if image_embedding is None:
            raise ValueError("形状相似性查询需要图像嵌入向量")

        # 确保向量格式正确
        if isinstance(image_embedding, list):
            image_embedding = np.array(image_embedding)

        # 构建前序结果过滤条件
        previous_filter = ""
        if previous_results:
            previous_ids = [res.get("entity", {}).get("id") for res in previous_results
                          if res.get("entity", {}).get("id")]
            if previous_ids:
                previous_filter = f"AND n.id IN {previous_ids}"

        # 构建节点标签过滤
        entity_labels = " OR ".join([f"n:{entity}" for entity in target_entities])

        # 使用Neo4j向量索引进行相似性搜索
        query = f"""
        CALL db.index.vector.queryNodes(
            'shape_embedding_index',
            $top_k,
            $query_vector
        ) YIELD node AS n, score
        WHERE ({entity_labels}) AND score >= $similarity_threshold {previous_filter}
        RETURN n.id AS id, labels(n)[0] AS type, n.name AS name,
               n.description AS description, score AS similarity_score,
               n AS properties
        ORDER BY score DESC
        """

        params = {
            "query_vector": image_embedding.tolist(),
            "top_k": top_k * 2,  # 获取更多结果以便过滤
            "similarity_threshold": similarity_threshold
        }

        with self.kg.driver.session() as session:
            result = session.run(query, params)
            results = []
            for record in result:
                result_item = {
                    "id": record["id"],
                    "type": record["type"],
                    "name": record["name"],
                    "description": record["description"],
                    "similarity_score": record["similarity_score"],
                    "properties": dict(record["properties"])
                }
                results.append(result_item)

        # 应用额外过滤器
        if filters:
            results = self._apply_filters(results, filters)

        return results[:top_k]

    async def _execute_description_similarity_query(self,
                                                   text_query: str,
                                                   target_entities: List[str],
                                                   filters: Dict[str, Any],
                                                   top_k: int,
                                                   similarity_threshold: float,
                                                   previous_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行基于描述嵌入的语义相似性查询"""

        if not text_query:
            raise ValueError("描述相似性查询需要文本查询")

        # 这里需要将文本转换为嵌入向量
        # 假设我们有一个文本嵌入服务
        text_embedding = await self._get_text_embedding(text_query)

        # 构建前序结果过滤条件
        previous_filter = ""
        if previous_results:
            previous_ids = [res.get("entity", {}).get("id") for res in previous_results
                          if res.get("entity", {}).get("id")]
            if previous_ids:
                previous_filter = f"AND n.id IN {previous_ids}"

        # 构建节点标签过滤
        entity_labels = " OR ".join([f"n:{entity}" for entity in target_entities])

        # 使用description_embedding进行相似性搜索
        query = f"""
        MATCH (n)
        WHERE ({entity_labels}) AND n.description_embedding IS NOT NULL {previous_filter}
        WITH n, gds.similarity.cosine($text_embedding, n.description_embedding) AS similarity
        WHERE similarity >= $similarity_threshold
        RETURN n.id AS id, labels(n)[0] AS type, n.name AS name,
               n.description AS description, similarity AS similarity_score,
               n AS properties
        ORDER BY similarity DESC
        LIMIT $top_k
        """

        params = {
            "text_embedding": text_embedding,
            "similarity_threshold": similarity_threshold,
            "top_k": top_k
        }

        with self.kg.driver.session() as session:
            result = session.run(query, params)
            results = []
            for record in result:
                result_item = {
                    "id": record["id"],
                    "type": record["type"],
                    "name": record["name"],
                    "description": record["description"],
                    "similarity_score": record["similarity_score"],
                    "properties": dict(record["properties"])
                }
                results.append(result_item)

        # 应用额外过滤器
        if filters:
            results = self._apply_filters(results, filters)

        return results

    async def _get_text_embedding(self, text: str) -> List[float]:
        """获取文本的嵌入向量"""

        try:
            # 这里应该调用实际的文本嵌入服务
            # 例如使用sentence-transformers、OpenAI的embedding API或本地嵌入模型

            # 方案1: 使用sentence-transformers (推荐)
            # from sentence_transformers import SentenceTransformer
            # model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
            # embedding = model.encode(text)
            # return embedding.tolist()

            # 方案2: 使用OpenAI embedding API
            # import openai
            # response = openai.Embedding.create(input=text, model="text-embedding-ada-002")
            # return response['data'][0]['embedding']

            # 临时实现：使用随机向量模拟
            # 在实际部署时，请替换为真实的文本嵌入实现
            import numpy as np
            logging.warning("使用模拟的文本嵌入向量，请在生产环境中替换为真实实现")
            return np.random.rand(768).tolist()

        except Exception as e:
            logging.error(f"文本嵌入生成失败: {e}")
            # 返回零向量作为回退
            return [0.0] * 768
    
    async def _execute_hybrid_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行混合查询"""

        try:
            # 获取查询分解信息
            sub_queries = query_params.get("sub_queries", [])
            execution_strategy = query_params.get("execution_strategy", {})

            if not sub_queries:
                # 回退到简单的并行查询
                return await self._execute_simple_hybrid_query(query_params)

            # 根据执行策略选择处理方式
            if execution_strategy.get("parallel", True) and not execution_strategy.get("filter_chain", False):
                return await self._execute_parallel_hybrid_query(sub_queries, query_params)
            else:
                return await self._execute_sequential_hybrid_query(sub_queries, query_params)

        except Exception as e:
            logging.error(f"混合查询执行失败: {e}")
            raise

    async def _execute_simple_hybrid_query(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行简单的混合查询（回退方案）"""

        # 并行执行多种查询类型
        tasks = []

        # 元数据查询
        if query_params.get("text_query") or query_params.get("filters"):
            tasks.append(self._execute_metadata_query(query_params))

        # 向量相似性查询
        if query_params.get("image_embedding") is not None:
            tasks.append(self._execute_vector_similarity_query(query_params))

        # 结构查询
        if query_params.get("text_query"):
            tasks.append(self._execute_structure_query(query_params))

        # 等待所有查询完成
        if tasks:
            results_list = await asyncio.gather(*tasks, return_exceptions=True)

            # 合并结果
            all_results = []
            for results in results_list:
                if isinstance(results, list):
                    all_results.extend(results)

            # 去重并排序
            unique_results = self._deduplicate_results(all_results)
            return self._rank_hybrid_results(unique_results)

        return []

    async def _execute_parallel_hybrid_query(self,
                                           sub_queries: List[Dict[str, Any]],
                                           base_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行并行混合查询"""

        tasks = []

        for sub_query in sub_queries:
            # 为每个子查询构建参数
            sub_params = {**base_params}
            sub_params.update({
                "target_entities": sub_query.get("target_entities", ["Assembly", "Part"]),
                "required_attributes": sub_query.get("required_attributes", []),
                "embedding_type": sub_query.get("embedding_type", "shape_embedding")
            })

            # 根据查询类型创建任务
            if sub_query["type"] == "metadata":
                tasks.append(self._execute_metadata_query(sub_params))
            elif sub_query["type"] == "structure":
                tasks.append(self._execute_structure_query(sub_params))
            elif sub_query["type"] == "vector_similarity":
                tasks.append(self._execute_vector_similarity_query(sub_params))

        # 并行执行所有查询
        if tasks:
            results_list = await asyncio.gather(*tasks, return_exceptions=True)

            # 合并结果
            all_results = []
            for results in results_list:
                if isinstance(results, list):
                    all_results.extend(results)
                elif not isinstance(results, Exception):
                    all_results.append(results)

            # 去重并排序
            unique_results = self._deduplicate_results(all_results)
            return self._rank_hybrid_results(unique_results)

        return []

    async def _execute_sequential_hybrid_query(self,
                                             sub_queries: List[Dict[str, Any]],
                                             base_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """执行顺序混合查询（支持过滤链）"""

        # 按优先级排序子查询
        sorted_queries = sorted(sub_queries, key=lambda x: x.get("priority", 5))

        all_results = []
        query_results_cache = {}  # 缓存每个查询的结果

        for i, sub_query in enumerate(sorted_queries):
            try:
                # 构建子查询参数
                sub_params = {**base_params}
                sub_params.update({
                    "target_entities": sub_query.get("target_entities", ["Assembly", "Part"]),
                    "required_attributes": sub_query.get("required_attributes", []),
                    "embedding_type": sub_query.get("embedding_type", "shape_embedding")
                })

                # 处理依赖关系
                depends_on = sub_query.get("depends_on", [])
                if depends_on:
                    # 获取前置查询的结果作为过滤条件
                    previous_results = []
                    for dep_index in depends_on:
                        if dep_index < len(query_results_cache):
                            previous_results.extend(query_results_cache.get(dep_index, []))

                    if previous_results:
                        sub_params["previous_results"] = previous_results
                    else:
                        # 如果没有前置结果，跳过此查询
                        logging.warning(f"子查询 {i} 依赖的前置查询没有结果，跳过")
                        continue

                # 执行子查询
                if sub_query["type"] == "metadata":
                    results = await self._execute_metadata_query(sub_params)
                elif sub_query["type"] == "structure":
                    results = await self._execute_structure_query(sub_params)
                elif sub_query["type"] == "vector_similarity":
                    results = await self._execute_vector_similarity_query(sub_params)
                else:
                    logging.warning(f"不支持的查询类型: {sub_query['type']}")
                    continue

                # 缓存结果
                query_results_cache[i] = results
                all_results.extend(results)

                logging.info(f"子查询 {i} ({sub_query['type']}) 完成，返回 {len(results)} 个结果")

            except Exception as e:
                logging.error(f"子查询 {i} 执行失败: {e}")
                continue

        # 去重并排序
        unique_results = self._deduplicate_results(all_results)
        return self._rank_hybrid_results(unique_results)

    def _filter_by_previous_results(self,
                                   current_results: List[Dict[str, Any]],
                                   previous_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据前序查询结果过滤当前结果"""

        if not previous_results:
            return current_results

        # 提取前序结果的ID集合
        previous_ids = set()
        for res in previous_results:
            if "entity" in res and "id" in res["entity"]:
                previous_ids.add(res["entity"]["id"])
            elif "id" in res:
                previous_ids.add(res["id"])

        # 过滤当前结果
        filtered_results = []
        for res in current_results:
            result_id = None
            if "entity" in res and "id" in res["entity"]:
                result_id = res["entity"]["id"]
            elif "id" in res:
                result_id = res["id"]

            if result_id and result_id in previous_ids:
                filtered_results.append(res)

        return filtered_results
    
    async def _execute_text_search(self, text_query: str) -> List[Dict[str, Any]]:
        """执行文本搜索"""
        
        # 简单的文本匹配查询
        query = """
        MATCH (n)
        WHERE toLower(n.name) CONTAINS toLower($text)
           OR toLower(n.material) CONTAINS toLower($text)
           OR toLower(n.category) CONTAINS toLower($text)
           OR toLower(n.industry) CONTAINS toLower($text)
        RETURN n, labels(n) as types
        LIMIT 50
        """
        
        params = {"text": text_query}
        
        with self.kg.driver.session() as session:
            result = session.run(query, params)
            return [{"entity": dict(record["n"]), "types": record["types"]} 
                   for record in result]
    
    def _parse_structure_intent(self, text_query: str) -> Dict[str, Any]:
        """解析结构查询意图"""
        
        text = text_query.lower()
        
        if any(keyword in text for keyword in ["装配体", "assembly", "结构"]):
            return {"type": "assembly_structure", "target": "assembly"}
        elif any(keyword in text for keyword in ["零件", "part", "组件"]):
            return {"type": "part_relationships", "target": "part"}
        elif any(keyword in text for keyword in ["特征", "feature", "孔", "槽"]):
            return {"type": "feature_search", "target": "feature"}
        else:
            return {"type": "general_structure", "target": "all"}
    
    async def _query_assembly_structure(self, intent: Dict[str, Any], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询装配体结构"""
        
        query = """
        MATCH (a:Assembly)
        OPTIONAL MATCH (a)-[:hasSubAssembly]->(sub:SubAssembly)
        OPTIONAL MATCH (a)-[:hasPart]->(p:Part)
        OPTIONAL MATCH (p)-[:hasFeature]->(f:Feature)
        RETURN a, collect(distinct sub) as subassemblies, 
               collect(distinct p) as parts, collect(distinct f) as features
        LIMIT 20
        """
        
        with self.kg.driver.session() as session:
            result = session.run(query)
            structures = []
            for record in result:
                structure = {
                    "assembly": dict(record["a"]),
                    "subassemblies": [dict(sub) for sub in record["subassemblies"]],
                    "parts": [dict(part) for part in record["parts"]],
                    "features": [dict(feature) for feature in record["features"]]
                }
                structures.append(structure)
            return structures
    
    async def _query_part_relationships(self, intent: Dict[str, Any], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询零件关系"""
        
        query = """
        MATCH (p:Part)
        OPTIONAL MATCH (parent)-[:hasPart]->(p)
        OPTIONAL MATCH (p)-[:hasFeature]->(f:Feature)
        RETURN p, parent, collect(f) as features
        LIMIT 50
        """
        
        with self.kg.driver.session() as session:
            result = session.run(query)
            relationships = []
            for record in result:
                relationship = {
                    "part": dict(record["p"]),
                    "parent": dict(record["parent"]) if record["parent"] else None,
                    "features": [dict(feature) for feature in record["features"]]
                }
                relationships.append(relationship)
            return relationships
    
    async def _query_features(self, intent: Dict[str, Any], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询特征"""
        
        query = """
        MATCH (f:Feature)
        OPTIONAL MATCH (p:Part)-[:hasFeature]->(f)
        RETURN f, p
        LIMIT 100
        """
        
        with self.kg.driver.session() as session:
            result = session.run(query)
            features = []
            for record in result:
                feature = {
                    "feature": dict(record["f"]),
                    "part": dict(record["p"]) if record["p"] else None
                }
                features.append(feature)
            return features
    
    async def _query_general_structure(self, intent: Dict[str, Any], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """通用结构查询"""
        
        query = """
        MATCH (n)-[r]->(m)
        RETURN n, type(r) as relationship, m
        LIMIT 100
        """
        
        with self.kg.driver.session() as session:
            result = session.run(query)
            structures = []
            for record in result:
                structure = {
                    "source": dict(record["n"]),
                    "relationship": record["relationship"],
                    "target": dict(record["m"])
                }
                structures.append(structure)
            return structures
    
    def _apply_filters(self, results: List[Dict[str, Any]], filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用过滤器"""
        
        filtered_results = []
        for result in results:
            match = True
            for key, value in filters.items():
                if key in result and result[key] != value:
                    match = False
                    break
            if match:
                filtered_results.append(result)
        
        return filtered_results
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重结果"""
        
        seen_ids = set()
        unique_results = []
        
        for result in results:
            # 尝试获取唯一标识符
            entity_id = None
            if "entity" in result and "id" in result["entity"]:
                entity_id = result["entity"]["id"]
            elif "id" in result:
                entity_id = result["id"]
            elif "assembly" in result and "id" in result["assembly"]:
                entity_id = result["assembly"]["id"]
            
            if entity_id and entity_id not in seen_ids:
                seen_ids.add(entity_id)
                unique_results.append(result)
            elif entity_id is None:
                # 如果没有ID，直接添加
                unique_results.append(result)
        
        return unique_results
    
    def _rank_hybrid_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """对混合查询结果进行排序"""
        
        # 简单的排序策略：优先考虑相似度分数
        def get_score(result):
            if "similarity_score" in result:
                return result["similarity_score"]
            return 0.5  # 默认分数
        
        return sorted(results, key=get_score, reverse=True)
    
    def close(self):
        """关闭数据库连接"""
        if self.kg:
            self.kg.close()
        logging.info(f"多模态查询智能体 {self.agent_id} 已关闭")
