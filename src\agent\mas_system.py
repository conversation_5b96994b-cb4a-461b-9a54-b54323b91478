"""
多智能体系统 (Multi-Agent System) 主类
实现完整的多智能体协作工作流
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta

from .base_agent import (
    BaseAgent, AgentMessage, QueryResult, MessageType, QueryType,
    MultimodalQuery, RetrievalStrategy
)
from .orchestrator_agent import OrchestratorAgent
from .multimodal_query_agent import MultimodalQueryAgent
from .fusion_agent import FusionAgent


class MultiAgentSystem:
    """多智能体系统主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化多智能体系统
        
        Args:
            config: 系统配置参数
        """
        self.config = config or {}
        
        # 初始化智能体
        self.orchestrator = OrchestratorAgent()
        self.query_agent = MultimodalQueryAgent()
        self.fusion_agent = FusionAgent()
        
        # 智能体注册
        self.agents: Dict[str, BaseAgent] = {
            self.orchestrator.agent_id: self.orchestrator,
            self.query_agent.agent_id: self.query_agent,
            self.fusion_agent.agent_id: self.fusion_agent
        }
        
        # 注册智能体关系
        self.orchestrator.register_query_agent(self.query_agent.agent_id)
        self.orchestrator.register_fusion_agent(self.fusion_agent.agent_id)
        
        # 系统状态
        self.is_running = False
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        
        logging.info("多智能体系统初始化完成")
    
    async def start(self):
        """启动多智能体系统"""
        self.is_running = True
        logging.info("多智能体系统已启动")
    
    async def stop(self):
        """停止多智能体系统"""
        self.is_running = False
        
        # 关闭数据库连接
        if hasattr(self.query_agent, 'close'):
            self.query_agent.close()
        
        logging.info("多智能体系统已停止")
    
    async def process_user_query(self, 
                               user_query: Union[str, Dict[str, Any], MultimodalQuery],
                               session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        处理用户查询的主入口点
        
        Args:
            user_query: 用户查询（文本、字典或MultimodalQuery对象）
            session_id: 会话ID，用于跟踪多轮对话
            
        Returns:
            Dict: 查询结果
        """
        if not self.is_running:
            raise RuntimeError("多智能体系统未启动")
        
        start_time = datetime.now()
        
        try:
            # 1. 标准化用户查询
            multimodal_query = self._normalize_user_query(user_query)
            
            # 2. 创建会话
            if not session_id:
                session_id = f"session_{multimodal_query.query_id}"
            
            self._create_session(session_id, multimodal_query)
            
            # 3. 发送查询到编排智能体
            query_message = AgentMessage(
                sender="user",
                receiver=self.orchestrator.agent_id,
                message_type=MessageType.QUERY,
                content={
                    "query": multimodal_query.__dict__,
                    "session_id": session_id
                }
            )
            
            # 4. 处理查询
            response = await self.orchestrator.process_message(query_message)
            
            # 5. 解析响应
            result = self._parse_orchestrator_response(response)
            
            # 6. 更新会话状态
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_session(session_id, result, execution_time)
            
            return {
                "success": True,
                "query_id": multimodal_query.query_id,
                "session_id": session_id,
                "results": result.get("results", []),
                "metadata": {
                    "execution_time": execution_time,
                    "result_count": len(result.get("results", [])),
                    **result.get("metadata", {})
                }
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logging.error(f"处理用户查询失败: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "execution_time": execution_time
            }
    
    async def process_multimodal_query_advanced(self, 
                                              multimodal_query: MultimodalQuery,
                                              custom_strategy: Optional[RetrievalStrategy] = None) -> Dict[str, Any]:
        """
        高级多模态查询处理，支持自定义策略
        
        Args:
            multimodal_query: 多模态查询对象
            custom_strategy: 自定义检索策略
            
        Returns:
            Dict: 详细的查询结果
        """
        start_time = datetime.now()
        
        try:
            # 1. 如果提供了自定义策略，直接执行
            if custom_strategy:
                results = await self._execute_custom_strategy(multimodal_query, custom_strategy)
            else:
                # 2. 使用编排智能体的标准流程
                task = {
                    "query_id": multimodal_query.query_id,
                    "multimodal_query": multimodal_query
                }
                
                orchestrator_result = await self.orchestrator.execute_task(task)
                results = orchestrator_result.results
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                "success": True,
                "query_id": multimodal_query.query_id,
                "results": results,
                "execution_time": execution_time,
                "strategy_used": custom_strategy.__dict__ if custom_strategy else "auto"
            }
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logging.error(f"高级查询处理失败: {e}")
            
            return {
                "success": False,
                "error": str(e),
                "execution_time": execution_time
            }
    
    async def execute_parallel_queries(self, 
                                     queries: List[MultimodalQuery],
                                     max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """
        并行执行多个查询
        
        Args:
            queries: 查询列表
            max_concurrent: 最大并发数
            
        Returns:
            List[Dict]: 查询结果列表
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single_query(query):
            async with semaphore:
                return await self.process_multimodal_query_advanced(query)
        
        tasks = [process_single_query(query) for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "query_id": queries[i].query_id,
                    "error": str(result)
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def _normalize_user_query(self, user_query: Union[str, Dict[str, Any], MultimodalQuery]) -> MultimodalQuery:
        """标准化用户查询"""
        
        if isinstance(user_query, MultimodalQuery):
            return user_query
        elif isinstance(user_query, str):
            return MultimodalQuery(text_query=user_query)
        elif isinstance(user_query, dict):
            return MultimodalQuery(**user_query)
        else:
            raise ValueError(f"不支持的查询类型: {type(user_query)}")
    
    def _create_session(self, session_id: str, multimodal_query: MultimodalQuery):
        """创建会话"""
        self.active_sessions[session_id] = {
            "session_id": session_id,
            "start_time": datetime.now(),
            "query": multimodal_query,
            "status": "processing",
            "messages": []
        }
    
    def _update_session(self, session_id: str, result: Dict[str, Any], execution_time: float):
        """更新会话状态"""
        if session_id in self.active_sessions:
            self.active_sessions[session_id].update({
                "status": "completed",
                "result": result,
                "execution_time": execution_time,
                "end_time": datetime.now()
            })
    
    def _parse_orchestrator_response(self, response: AgentMessage) -> Dict[str, Any]:
        """解析编排智能体的响应"""
        
        if response.message_type == MessageType.ERROR:
            raise Exception(response.content.get("error", "编排智能体处理失败"))
        
        content = response.content
        result_data = content.get("result", {})
        
        if isinstance(result_data, dict) and "results" in result_data:
            return {
                "results": result_data["results"],
                "metadata": result_data.get("metadata", {})
            }
        else:
            return {
                "results": [],
                "metadata": {"raw_response": content}
            }
    
    async def _execute_custom_strategy(self, 
                                     multimodal_query: MultimodalQuery, 
                                     strategy: RetrievalStrategy) -> List[Dict[str, Any]]:
        """执行自定义检索策略"""
        
        all_results = []
        
        # 根据策略执行不同类型的查询
        if strategy.parallel_execution:
            # 并行执行
            tasks = []
            for query_type in strategy.query_types:
                task = self._create_query_task_for_agent(multimodal_query, query_type, strategy)
                tasks.append(task)
            
            results_list = await asyncio.gather(*tasks, return_exceptions=True)
            
            for results in results_list:
                if isinstance(results, list):
                    all_results.extend(results)
        else:
            # 顺序执行
            for query_type in strategy.query_types:
                try:
                    results = await self._create_query_task_for_agent(multimodal_query, query_type, strategy)
                    all_results.extend(results)
                except Exception as e:
                    logging.warning(f"查询类型 {query_type} 执行失败: {e}")
        
        # 如果需要融合且有多个结果源
        if strategy.should_use_fusion() and len(strategy.query_types) > 1:
            # 按查询类型分组结果
            results_by_type = {}
            for i, query_type in enumerate(strategy.query_types):
                results_by_type[query_type.value] = []
            
            # 这里需要更复杂的逻辑来分组结果
            # 为简化，直接使用融合智能体
            fusion_task = {
                "query_id": multimodal_query.query_id,
                "results_list": [all_results],  # 简化处理
                "fusion_params": {
                    "strategy": "consensus_based",
                    "confidence_threshold": strategy.confidence_threshold,
                    "max_results": strategy.max_results_per_query
                },
                "multimodal_query": multimodal_query
            }
            
            fusion_result = await self.fusion_agent.execute_task(fusion_task)
            if fusion_result.is_successful():
                return fusion_result.results
        
        return all_results[:strategy.max_results_per_query]
    
    async def _create_query_task_for_agent(self, 
                                         multimodal_query: MultimodalQuery, 
                                         query_type: QueryType, 
                                         strategy: RetrievalStrategy) -> List[Dict[str, Any]]:
        """为查询智能体创建查询任务"""
        
        # 构建查询参数
        query_params = {
            "text_query": multimodal_query.text_query,
            "filters": multimodal_query.filters,
            "top_k": strategy.max_results_per_query,
            "similarity_threshold": strategy.confidence_threshold
        }
        
        # 如果有图像数据，添加到参数中
        if multimodal_query.has_image():
            query_params["image_embedding"] = multimodal_query.image_data
        
        # 创建任务
        task = {
            "query_id": multimodal_query.query_id,
            "query_type": query_type.value,
            "query_params": query_params
        }
        
        # 执行任务
        result = await self.query_agent.execute_task(task)
        
        if result.is_successful():
            return result.results
        else:
            logging.warning(f"查询任务执行失败: {result.error}")
            return []
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "is_running": self.is_running,
            "active_sessions": len(self.active_sessions),
            "agents": {
                agent_id: agent.get_status() 
                for agent_id, agent in self.agents.items()
            }
        }
    
    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        return self.active_sessions.get(session_id)
    
    def clear_sessions(self, older_than_hours: int = 24):
        """清理旧会话"""
        cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
        
        sessions_to_remove = []
        for session_id, session_data in self.active_sessions.items():
            if session_data["start_time"] < cutoff_time:
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            del self.active_sessions[session_id]
        
        logging.info(f"清理了 {len(sessions_to_remove)} 个旧会话")


# 便捷函数
async def create_mas_system(config: Optional[Dict[str, Any]] = None) -> MultiAgentSystem:
    """创建并启动多智能体系统"""
    mas = MultiAgentSystem(config)
    await mas.start()
    return mas


async def quick_query(query: Union[str, Dict[str, Any]], 
                     config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """快速查询函数"""
    mas = await create_mas_system(config)
    try:
        result = await mas.process_user_query(query)
        return result
    finally:
        await mas.stop()
