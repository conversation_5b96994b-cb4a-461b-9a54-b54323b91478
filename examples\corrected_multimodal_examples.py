"""
修正的多模态查询示例
展示正确的LLM使用方式和图片处理策略
"""

import asyncio
import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.agent import MultiAgentSystem, MultimodalQuery, RetrievalStrategy, QueryType


async def example_image_only_query():
    """示例1: 纯图片输入查询 - 不使用LLM"""
    print("=" * 60)
    print("示例1: 纯图片输入查询 - 直接shape_embedding相似性检索")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 模拟图片的shape_embedding向量
        mock_shape_embedding = np.random.rand(768).tolist()
        
        # 创建纯图片查询
        image_query = MultimodalQuery(
            image_data=mock_shape_embedding,  # 图片特征向量
            query_intent="基于图片查找形状相似的零件"
        )
        
        print(f"输入模态: 图片")
        print(f"处理策略: 直接进行shape_embedding相似性检索，不使用LLM")
        print(f"预期查询类型: vector_similarity")
        print(f"使用嵌入: shape_embedding")
        
        result = await mas.process_multimodal_query_advanced(image_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
        
        # 显示前几个结果
        for i, res in enumerate(result.get('results', [])[:3]):
            print(f"\n  相似结果 {i+1}:")
            print(f"    ID: {res.get('id', 'N/A')}")
            print(f"    类型: {res.get('type', 'N/A')}")
            print(f"    相似度: {res.get('confidence_score', 0):.3f}")
    
    finally:
        await mas.stop()


async def example_text_with_llm():
    """示例2: 文字输入 - 使用LLM进行查询分解"""
    print("\n" + "=" * 60)
    print("示例2: 文字输入 - 使用MultiProviderLLM.chat进行查询分解")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建复杂的文字查询
        text_query = MultimodalQuery(
            text_query="查找钢材制造的发动机装配体，包含螺栓连接结构",
            query_intent="复杂的多属性和结构查询"
        )
        
        print(f"输入模态: 文字")
        print(f"查询文本: {text_query.text_query}")
        print(f"处理策略: 使用LLM.chat方法进行智能查询分解")
        print(f"预期分解:")
        print(f"  1. 元数据查询: material=steel")
        print(f"  2. 语义查询: 发动机装配体 (description_embedding)")
        print(f"  3. 结构查询: 螺栓连接结构")
        
        result = await mas.process_multimodal_query_advanced(text_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
        
        if result['success']:
            print(f"  LLM分解成功，执行了智能查询策略")
        else:
            print(f"  可能回退到规则分解: {result.get('error', 'N/A')}")
    
    finally:
        await mas.stop()


async def example_text_rule_based():
    """示例3: 文字输入 - 规则分解（LLM不可用时）"""
    print("\n" + "=" * 60)
    print("示例3: 文字输入 - 基于规则的查询分解")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建包含明确关键词的查询
        rule_query = MultimodalQuery(
            text_query="查找铝合金材料的汽车零件",
            filters={"category": "automotive"},
            query_intent="基于规则识别的元数据查询"
        )
        
        print(f"输入模态: 文字")
        print(f"查询文本: {rule_query.text_query}")
        print(f"处理策略: 基于关键词规则进行查询分解")
        print(f"识别的关键词:")
        print(f"  - 材料关键词: 铝合金 -> material=aluminum")
        print(f"  - 类别关键词: 汽车 -> category=automotive")
        print(f"预期查询类型: metadata")
        
        result = await mas.process_multimodal_query_advanced(rule_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
    
    finally:
        await mas.stop()


async def example_semantic_similarity():
    """示例4: 语义相似性查询 - description_embedding"""
    print("\n" + "=" * 60)
    print("示例4: 语义相似性查询 - description_embedding")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建语义查询
        semantic_query = MultimodalQuery(
            text_query="查找功能类似发动机的装配体",
            query_intent="基于功能描述的语义相似性搜索"
        )
        
        print(f"输入模态: 文字")
        print(f"查询文本: {semantic_query.text_query}")
        print(f"处理策略: 文本 -> 嵌入向量 -> description_embedding相似性搜索")
        print(f"预期查询类型: vector_similarity")
        print(f"使用嵌入: description_embedding")
        
        result = await mas.process_multimodal_query_advanced(semantic_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
        
        # 显示结果
        for i, res in enumerate(result.get('results', [])[:3]):
            data = res.get('data', {})
            print(f"\n  语义相似结果 {i+1}:")
            print(f"    名称: {data.get('name', 'N/A')}")
            print(f"    描述: {data.get('description', 'N/A')}")
            print(f"    相似度: {res.get('confidence_score', 0):.3f}")
    
    finally:
        await mas.stop()


async def example_hybrid_with_filter_chain():
    """示例5: 混合查询 - 过滤链处理"""
    print("\n" + "=" * 60)
    print("示例5: 混合查询 - 过滤链处理")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建复杂的混合查询
        hybrid_query = MultimodalQuery(
            text_query="查找钢材制造的发动机装配体结构",
            filters={"material": "steel"},
            query_intent="多步骤过滤链查询"
        )
        
        print(f"输入模态: 文字")
        print(f"查询文本: {hybrid_query.text_query}")
        print(f"处理策略: 顺序执行多个查询，后续查询使用前序结果过滤")
        print(f"预期执行顺序:")
        print(f"  1. 元数据查询: material=steel (获取钢材零件)")
        print(f"  2. 语义查询: 发动机装配体 (在步骤1结果中搜索)")
        print(f"  3. 结构查询: 装配体结构 (在步骤2结果中搜索)")
        
        result = await mas.process_multimodal_query_advanced(hybrid_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
        
        if result['success']:
            print(f"  过滤链执行成功，结果经过多步骤精炼")
    
    finally:
        await mas.stop()


async def example_llm_configuration():
    """示例6: LLM配置说明"""
    print("\n" + "=" * 60)
    print("示例6: LLM配置说明")
    print("=" * 60)
    
    print("LLM配置方式:")
    print("1. 环境变量配置:")
    print("   export LLM_PROVIDER=openai")
    print("   export LLM_API_URL=https://api.openai.com")
    print("   export LLM_API_KEY=your-api-key")
    print("   export LLM_MODEL=gpt-3.5-turbo")
    
    print("\n2. 支持的LLM提供商:")
    print("   - OpenAI: provider='openai'")
    print("   - Azure OpenAI: provider='azure'")
    print("   - Ollama: provider='ollama'")
    print("   - Gemini: provider='gemini'")
    
    print("\n3. 图片处理策略:")
    print("   - 图片输入: 直接使用shape_embedding，不经过LLM")
    print("   - 文字输入: 使用LLM.chat方法进行查询分解")
    print("   - 回退机制: LLM不可用时使用规则分解")
    
    print("\n4. 文本嵌入处理:")
    print("   - 推荐使用sentence-transformers")
    print("   - 或使用OpenAI embedding API")
    print("   - 当前使用模拟向量（需要替换为真实实现）")


async def main():
    """运行所有修正的示例"""
    print("修正的多模态查询系统演示")
    print("正确使用MultiProviderLLM.chat和图片处理策略")
    print("=" * 60)
    
    examples = [
        example_image_only_query,
        example_text_with_llm,
        example_text_rule_based,
        example_semantic_similarity,
        example_hybrid_with_filter_chain,
        example_llm_configuration
    ]
    
    for example in examples:
        try:
            await example()
            await asyncio.sleep(1)  # 短暂暂停
        except Exception as e:
            print(f"\n示例执行失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("所有修正示例执行完成")
    print("\n关键修正点:")
    print("✅ 使用MultiProviderLLM.chat方法")
    print("✅ 图片输入直接进行shape_embedding检索")
    print("✅ 文字输入使用LLM智能分解")
    print("✅ LLM不可用时回退到规则分解")
    print("✅ 支持description_embedding语义搜索")
    print("✅ 实现过滤链和依赖关系处理")


if __name__ == "__main__":
    asyncio.run(main())
