"""
编排智能体 (Orchestrator Agent)
负责查询分解、策略规划和智能体委派
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from .base_agent import (
    BaseAgent, AgentMessage, QueryResult, MessageType, QueryType,
    MultimodalQuery, RetrievalStrategy
)
from ..models.LLM import LLMManager


class OrchestratorAgent(BaseAgent):
    """编排智能体 - 系统的总调度中心"""
    
    def __init__(self, agent_id: str = "orchestrator", name: str = "编排智能体"):
        super().__init__(agent_id, name)
        self.llm_manager = LLMManager()
        self.query_agents: List[str] = []
        self.fusion_agent: Optional[str] = None
        self.active_queries: Dict[str, Dict[str, Any]] = {}
        
    def register_query_agent(self, agent_id: str):
        """注册查询智能体"""
        if agent_id not in self.query_agents:
            self.query_agents.append(agent_id)
            
    def register_fusion_agent(self, agent_id: str):
        """注册融合智能体"""
        self.fusion_agent = agent_id
        
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理接收到的消息"""
        self.log_message(message)
        
        if message.message_type == MessageType.QUERY:
            # 处理用户查询
            return await self._handle_user_query(message)
        elif message.message_type == MessageType.RESPONSE:
            # 处理其他智能体的响应
            return await self._handle_agent_response(message)
        else:
            # 返回错误消息
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"不支持的消息类型: {message.message_type}"}
            )
    
    async def execute_task(self, task: Dict[str, Any]) -> QueryResult:
        """执行编排任务"""
        query_id = task.get("query_id", "")
        multimodal_query = task.get("multimodal_query")
        
        if not multimodal_query:
            return QueryResult(
                query_id=query_id,
                query_type=QueryType.HYBRID,
                error="缺少多模态查询数据"
            )
        
        try:
            # 1. 查询分解和意图理解
            decomposition_result = await self._decompose_query(multimodal_query)
            
            # 2. 策略规划
            strategy = await self._plan_strategy(multimodal_query, decomposition_result)
            
            # 3. 执行检索
            results = await self._execute_retrieval_strategy(strategy, multimodal_query)
            
            return QueryResult(
                query_id=query_id,
                query_type=QueryType.HYBRID,
                results=results,
                metadata={
                    "strategy": strategy.__dict__,
                    "decomposition": decomposition_result
                }
            )
            
        except Exception as e:
            return QueryResult(
                query_id=query_id,
                query_type=QueryType.HYBRID,
                error=str(e)
            )
    
    async def _handle_user_query(self, message: AgentMessage) -> AgentMessage:
        """处理用户查询"""
        try:
            # 解析多模态查询
            query_data = message.content.get("query", {})
            multimodal_query = MultimodalQuery(**query_data)
            
            # 执行编排任务
            task = {
                "query_id": multimodal_query.query_id,
                "multimodal_query": multimodal_query
            }
            
            result = await self.execute_task(task)
            
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.RESPONSE,
                content={
                    "query_id": multimodal_query.query_id,
                    "result": result.__dict__
                }
            )
            
        except Exception as e:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"处理查询失败: {str(e)}"}
            )
    
    async def _handle_agent_response(self, message: AgentMessage) -> AgentMessage:
        """处理其他智能体的响应"""
        query_id = message.content.get("query_id")
        if query_id and query_id in self.active_queries:
            # 更新活跃查询状态
            self.active_queries[query_id]["responses"].append(message)
            
        return self.create_message(
            receiver=message.sender,
            message_type=MessageType.STATUS,
            content={"status": "响应已接收"}
        )
    
    async def _decompose_query(self, multimodal_query: MultimodalQuery) -> Dict[str, Any]:
        """查询分解和意图理解"""
        
        # 构建LLM提示
        prompt = self._build_decomposition_prompt(multimodal_query)
        
        try:
            # 使用LLM进行查询分解
            response = await self.llm_manager.generate_response(
                prompt=prompt,
                model_name="gpt-4",
                temperature=0.1
            )
            
            # 解析LLM响应
            decomposition = json.loads(response)
            
            return {
                "intent": decomposition.get("intent", "unknown"),
                "sub_queries": decomposition.get("sub_queries", []),
                "required_data_types": decomposition.get("required_data_types", []),
                "complexity_level": decomposition.get("complexity_level", "medium"),
                "estimated_execution_time": decomposition.get("estimated_execution_time", 5.0)
            }
            
        except Exception as e:
            # 回退到基于规则的分解
            return self._rule_based_decomposition(multimodal_query)
    
    def _build_decomposition_prompt(self, multimodal_query: MultimodalQuery) -> str:
        """构建查询分解的LLM提示"""
        
        prompt = f"""
你是一个CAD/机械设计领域的查询分解专家。请分析以下多模态查询并进行分解：

查询信息：
- 文本查询: {multimodal_query.text_query or "无"}
- 是否包含图像: {multimodal_query.has_image()}
- 是否包含草图: {multimodal_query.has_sketch()}
- 是否包含CAD片段: {multimodal_query.has_cad_fragment()}
- 过滤条件: {multimodal_query.filters}

知识图谱Schema：
- Assembly: 装配体 (属性: area, category, density, id, industry, mass, name, shape_embedding, volume)
- Part: 零件 (属性: area, density, id, mass, material, name, shape_embedding, volume)
- SubAssembly: 子装配体 (属性: area, density, id, mass, name, volume)
- Feature: 特征 (属性: diameter, id, length, name, type)

关系：
- Assembly -[:hasSubAssembly]-> SubAssembly
- Assembly/SubAssembly -[:hasPart]-> Part
- Part -[:hasFeature]-> Feature

请以JSON格式返回分解结果：
{{
    "intent": "查询意图描述",
    "sub_queries": [
        {{
            "type": "metadata|structure|vector_similarity",
            "description": "子查询描述",
            "target_entities": ["Assembly", "Part", "SubAssembly", "Feature"],
            "required_attributes": ["属性列表"],
            "priority": 1-5
        }}
    ],
    "required_data_types": ["需要的数据类型"],
    "complexity_level": "low|medium|high",
    "estimated_execution_time": 预估执行时间(秒)
}}
"""
        return prompt
    
    def _rule_based_decomposition(self, multimodal_query: MultimodalQuery) -> Dict[str, Any]:
        """基于规则的查询分解（回退方案）"""
        
        sub_queries = []
        
        # 基于文本查询的分解
        if multimodal_query.has_text():
            text = multimodal_query.text_query.lower()
            
            if any(keyword in text for keyword in ["装配体", "assembly", "总成"]):
                sub_queries.append({
                    "type": "metadata",
                    "description": "搜索装配体相关信息",
                    "target_entities": ["Assembly"],
                    "priority": 1
                })
            
            if any(keyword in text for keyword in ["零件", "part", "组件"]):
                sub_queries.append({
                    "type": "metadata", 
                    "description": "搜索零件相关信息",
                    "target_entities": ["Part"],
                    "priority": 2
                })
        
        # 基于图像/形状的分解
        if multimodal_query.has_image() or multimodal_query.has_sketch():
            sub_queries.append({
                "type": "vector_similarity",
                "description": "基于形状相似性搜索",
                "target_entities": ["Assembly", "Part"],
                "priority": 1
            })
        
        return {
            "intent": "基于规则的查询分解",
            "sub_queries": sub_queries,
            "required_data_types": ["Assembly", "Part"],
            "complexity_level": "medium",
            "estimated_execution_time": 3.0
        }
    
    async def _plan_strategy(self, 
                           multimodal_query: MultimodalQuery, 
                           decomposition: Dict[str, Any]) -> RetrievalStrategy:
        """策略规划"""
        
        sub_queries = decomposition.get("sub_queries", [])
        
        # 确定查询类型
        query_types = []
        for sub_query in sub_queries:
            query_type = QueryType(sub_query["type"])
            if query_type not in query_types:
                query_types.append(query_type)
        
        # 确定是否需要并行执行
        parallel_execution = len(query_types) > 1
        
        # 确定优先级顺序
        priority_order = [
            sub_query["description"] 
            for sub_query in sorted(sub_queries, key=lambda x: x.get("priority", 5))
        ]
        
        # 确定是否需要融合
        fusion_required = len(query_types) > 1 or multimodal_query.get_modality_count() > 1
        
        return RetrievalStrategy(
            query_types=query_types,
            parallel_execution=parallel_execution,
            priority_order=priority_order,
            fusion_required=fusion_required,
            confidence_threshold=0.6,
            max_results_per_query=20
        )
    
    async def _execute_retrieval_strategy(self, 
                                        strategy: RetrievalStrategy, 
                                        multimodal_query: MultimodalQuery) -> List[Dict[str, Any]]:
        """执行检索策略"""
        
        if not self.query_agents:
            raise Exception("没有可用的查询智能体")
        
        # 记录活跃查询
        self.active_queries[multimodal_query.query_id] = {
            "strategy": strategy,
            "query": multimodal_query,
            "responses": [],
            "start_time": datetime.now()
        }
        
        try:
            if strategy.parallel_execution:
                # 并行执行多个查询
                results = await self._execute_parallel_queries(strategy, multimodal_query)
            else:
                # 顺序执行查询
                results = await self._execute_sequential_queries(strategy, multimodal_query)
            
            # 如果需要融合且有融合智能体
            if strategy.should_use_fusion() and self.fusion_agent and len(results) > 1:
                results = await self._request_fusion(results, multimodal_query)
            
            return results
            
        finally:
            # 清理活跃查询
            if multimodal_query.query_id in self.active_queries:
                del self.active_queries[multimodal_query.query_id]
    
    async def _execute_parallel_queries(self, 
                                      strategy: RetrievalStrategy, 
                                      multimodal_query: MultimodalQuery) -> List[Dict[str, Any]]:
        """并行执行查询"""
        
        tasks = []
        for query_type in strategy.query_types:
            task = self._create_query_task(query_type, multimodal_query, strategy)
            tasks.append(task)
        
        # 并行执行所有查询任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤成功的结果
        valid_results = []
        for result in results:
            if isinstance(result, list):
                valid_results.extend(result)
            elif not isinstance(result, Exception):
                valid_results.append(result)
        
        return valid_results
    
    async def _execute_sequential_queries(self, 
                                        strategy: RetrievalStrategy, 
                                        multimodal_query: MultimodalQuery) -> List[Dict[str, Any]]:
        """顺序执行查询"""
        
        all_results = []
        for query_type in strategy.query_types:
            try:
                results = await self._create_query_task(query_type, multimodal_query, strategy)
                if results:
                    all_results.extend(results)
            except Exception as e:
                print(f"查询执行失败 {query_type}: {e}")
                continue
        
        return all_results
    
    async def _create_query_task(self, 
                               query_type: QueryType, 
                               multimodal_query: MultimodalQuery, 
                               strategy: RetrievalStrategy) -> List[Dict[str, Any]]:
        """创建查询任务"""
        
        # 这里应该向多模态查询智能体发送消息
        # 为了简化，这里返回模拟结果
        return [
            {
                "id": f"result_{query_type.value}_{i}",
                "type": query_type.value,
                "confidence": 0.8,
                "data": {"mock": "data"}
            }
            for i in range(3)
        ]
    
    async def _request_fusion(self, 
                            results: List[Dict[str, Any]], 
                            multimodal_query: MultimodalQuery) -> List[Dict[str, Any]]:
        """请求融合智能体处理结果"""
        
        # 这里应该向融合智能体发送消息
        # 为了简化，这里返回原始结果
        return results
