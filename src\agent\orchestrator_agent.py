"""
编排智能体 (Orchestrator Agent)
负责查询分解、策略规划和智能体委派
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from .base_agent import (
    BaseAgent, AgentMessage, QueryResult, MessageType, QueryType,
    MultimodalQuery, RetrievalStrategy
)
from ..models.LLM import LLMManager


class OrchestratorAgent(BaseAgent):
    """编排智能体 - 系统的总调度中心"""
    
    def __init__(self, agent_id: str = "orchestrator", name: str = "编排智能体"):
        super().__init__(agent_id, name)
        self.llm_manager = LLMManager()
        self.query_agents: List[str] = []
        self.fusion_agent: Optional[str] = None
        self.active_queries: Dict[str, Dict[str, Any]] = {}
        
    def register_query_agent(self, agent_id: str):
        """注册查询智能体"""
        if agent_id not in self.query_agents:
            self.query_agents.append(agent_id)
            
    def register_fusion_agent(self, agent_id: str):
        """注册融合智能体"""
        self.fusion_agent = agent_id
        
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理接收到的消息"""
        self.log_message(message)
        
        if message.message_type == MessageType.QUERY:
            # 处理用户查询
            return await self._handle_user_query(message)
        elif message.message_type == MessageType.RESPONSE:
            # 处理其他智能体的响应
            return await self._handle_agent_response(message)
        else:
            # 返回错误消息
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"不支持的消息类型: {message.message_type}"}
            )
    
    async def execute_task(self, task: Dict[str, Any]) -> QueryResult:
        """执行编排任务"""
        query_id = task.get("query_id", "")
        multimodal_query = task.get("multimodal_query")
        
        if not multimodal_query:
            return QueryResult(
                query_id=query_id,
                query_type=QueryType.HYBRID,
                error="缺少多模态查询数据"
            )
        
        try:
            # 1. 查询分解和意图理解
            decomposition_result = await self._decompose_query(multimodal_query)
            
            # 2. 策略规划
            strategy = await self._plan_strategy(multimodal_query, decomposition_result)
            
            # 3. 执行检索
            results = await self._execute_retrieval_strategy(strategy, multimodal_query)
            
            return QueryResult(
                query_id=query_id,
                query_type=QueryType.HYBRID,
                results=results,
                metadata={
                    "strategy": strategy.__dict__,
                    "decomposition": decomposition_result
                }
            )
            
        except Exception as e:
            return QueryResult(
                query_id=query_id,
                query_type=QueryType.HYBRID,
                error=str(e)
            )
    
    async def _handle_user_query(self, message: AgentMessage) -> AgentMessage:
        """处理用户查询"""
        try:
            # 解析多模态查询
            query_data = message.content.get("query", {})
            multimodal_query = MultimodalQuery(**query_data)
            
            # 执行编排任务
            task = {
                "query_id": multimodal_query.query_id,
                "multimodal_query": multimodal_query
            }
            
            result = await self.execute_task(task)
            
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.RESPONSE,
                content={
                    "query_id": multimodal_query.query_id,
                    "result": result.__dict__
                }
            )
            
        except Exception as e:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"处理查询失败: {str(e)}"}
            )
    
    async def _handle_agent_response(self, message: AgentMessage) -> AgentMessage:
        """处理其他智能体的响应"""
        query_id = message.content.get("query_id")
        if query_id and query_id in self.active_queries:
            # 更新活跃查询状态
            self.active_queries[query_id]["responses"].append(message)
            
        return self.create_message(
            receiver=message.sender,
            message_type=MessageType.STATUS,
            content={"status": "响应已接收"}
        )
    
    async def _decompose_query(self, multimodal_query: MultimodalQuery) -> Dict[str, Any]:
        """查询分解和意图理解"""
        
        # 构建LLM提示
        prompt = self._build_decomposition_prompt(multimodal_query)
        
        try:
            # 使用LLM进行查询分解
            response = await self.llm_manager.generate_response(
                prompt=prompt,
                model_name="gpt-4",
                temperature=0.1
            )
            
            # 解析LLM响应
            decomposition = json.loads(response)
            
            return {
                "intent": decomposition.get("intent", "unknown"),
                "sub_queries": decomposition.get("sub_queries", []),
                "required_data_types": decomposition.get("required_data_types", []),
                "complexity_level": decomposition.get("complexity_level", "medium"),
                "estimated_execution_time": decomposition.get("estimated_execution_time", 5.0)
            }
            
        except Exception as e:
            # 回退到基于规则的分解
            return self._rule_based_decomposition(multimodal_query)
    
    def _build_decomposition_prompt(self, multimodal_query: MultimodalQuery) -> str:
        """构建查询分解的LLM提示"""

        # 确定输入模态类型
        input_modalities = []
        if multimodal_query.has_text():
            input_modalities.append("文本")
        if multimodal_query.has_image():
            input_modalities.append("图像")
        if multimodal_query.has_sketch():
            input_modalities.append("草图")
        if multimodal_query.has_cad_fragment():
            input_modalities.append("CAD片段")

        prompt = f"""
你是一个CAD/机械设计领域的查询分解专家。请分析以下多模态查询并进行分解：

输入模态: {', '.join(input_modalities)}
查询信息：
- 文本查询: {multimodal_query.text_query or "无"}
- 是否包含图像: {multimodal_query.has_image()}
- 过滤条件: {multimodal_query.filters}

知识图谱Schema：
- Assembly: 装配体 (属性: area, category, density, id, industry, mass, name, description, shape_embedding, description_embedding, volume)
- Part: 零件 (属性: area, density, id, mass, material, name, description, shape_embedding, description_embedding, volume)
- SubAssembly: 子装配体 (属性: area, density, id, mass, name, volume)
- Feature: 特征 (属性: diameter, id, length, name, type)

关系：
- Assembly -[:hasSubAssembly]-> SubAssembly
- Assembly/SubAssembly -[:hasPart]-> Part
- Part -[:hasFeature]-> Feature

查询类型说明：
1. 图像输入 -> vector_similarity (使用shape_embedding进行形状相似性检索)
2. 文本输入可能的查询类型：
   - metadata: 基于属性值的精确匹配 (如材料、类别、名称等)
   - structure: 基于图结构的关系查询 (如"包含"、"组成"等)
   - vector_similarity: 基于description_embedding的语义相似性检索
   - hybrid: 混合多种查询类型，需要指定执行顺序和依赖关系

请以JSON格式返回分解结果：
{{
    "intent": "查询意图描述",
    "input_modality": "{', '.join(input_modalities)}",
    "sub_queries": [
        {{
            "type": "metadata|structure|vector_similarity",
            "description": "子查询描述",
            "target_entities": ["Assembly", "Part", "SubAssembly", "Feature"],
            "required_attributes": ["属性列表"],
            "priority": 1-5,
            "depends_on": ["前置查询的索引，如果有依赖关系"],
            "embedding_type": "shape_embedding|description_embedding",
            "cypher_hints": "Cypher查询提示"
        }}
    ],
    "execution_strategy": {{
        "parallel": true/false,
        "fusion_required": true/false,
        "filter_chain": "是否需要用前序结果过滤后续查询"
    }},
    "complexity_level": "low|medium|high",
    "estimated_execution_time": 预估执行时间(秒)
}}
"""
        return prompt
    
    def _rule_based_decomposition(self, multimodal_query: MultimodalQuery) -> Dict[str, Any]:
        """基于规则的查询分解（回退方案）"""

        sub_queries = []
        execution_strategy = {
            "parallel": True,
            "fusion_required": False,
            "filter_chain": False
        }

        # 1. 图像输入处理
        if multimodal_query.has_image() or multimodal_query.has_sketch():
            sub_queries.append({
                "type": "vector_similarity",
                "description": "基于形状相似性搜索",
                "target_entities": ["Assembly", "Part"],
                "priority": 1,
                "depends_on": [],
                "embedding_type": "shape_embedding",
                "cypher_hints": "使用shape_embedding向量索引进行相似性搜索"
            })
            execution_strategy["fusion_required"] = True

        # 2. 文本输入处理
        if multimodal_query.has_text():
            text = multimodal_query.text_query.lower()

            # 2.1 元数据查询识别
            metadata_keywords = {
                "材料": ["material", "材料", "钢", "铝", "塑料", "金属"],
                "类别": ["category", "类别", "机械", "电子", "汽车"],
                "行业": ["industry", "行业", "航空", "汽车", "机械"],
                "名称": ["name", "名称", "叫", "称为"]
            }

            for attr, keywords in metadata_keywords.items():
                if any(keyword in text for keyword in keywords):
                    sub_queries.append({
                        "type": "metadata",
                        "description": f"基于{attr}属性的元数据查询",
                        "target_entities": ["Assembly", "Part"],
                        "required_attributes": [attr.lower()],
                        "priority": 2,
                        "depends_on": [],
                        "embedding_type": None,
                        "cypher_hints": f"使用{attr}属性进行精确或模糊匹配"
                    })

            # 2.2 结构查询识别
            structure_keywords = ["包含", "组成", "结构", "关系", "子装配", "零件", "特征", "由", "构成"]
            if any(keyword in text for keyword in structure_keywords):
                sub_queries.append({
                    "type": "structure",
                    "description": "基于图结构的关系查询",
                    "target_entities": ["Assembly", "Part", "SubAssembly", "Feature"],
                    "priority": 3,
                    "depends_on": [],
                    "embedding_type": None,
                    "cypher_hints": "使用关系路径遍历查询装配体结构"
                })

            # 2.3 语义相似性查询识别
            semantic_keywords = ["相似", "类似", "像", "功能", "用途", "描述"]
            if any(keyword in text for keyword in semantic_keywords):
                sub_queries.append({
                    "type": "vector_similarity",
                    "description": "基于描述语义相似性搜索",
                    "target_entities": ["Assembly", "Part"],
                    "priority": 2,
                    "depends_on": [],
                    "embedding_type": "description_embedding",
                    "cypher_hints": "使用description_embedding向量索引进行语义相似性搜索"
                })
                execution_strategy["fusion_required"] = True

        # 3. 确定执行策略
        if len(sub_queries) > 1:
            execution_strategy["fusion_required"] = True

            # 检查是否需要过滤链
            has_metadata = any(q["type"] == "metadata" for q in sub_queries)
            has_others = any(q["type"] != "metadata" for q in sub_queries)
            if has_metadata and has_others:
                execution_strategy["filter_chain"] = True
                execution_strategy["parallel"] = False
                # 调整优先级，元数据查询优先
                for query in sub_queries:
                    if query["type"] == "metadata":
                        query["priority"] = 1
                    else:
                        query["priority"] = 2
                        query["depends_on"] = [0]  # 依赖第一个元数据查询

        return {
            "intent": "基于规则的查询分解",
            "input_modality": "图像" if multimodal_query.has_image() else "文本",
            "sub_queries": sub_queries,
            "execution_strategy": execution_strategy,
            "complexity_level": "medium" if len(sub_queries) > 1 else "low",
            "estimated_execution_time": 2.0 + len(sub_queries) * 1.0
        }
    
    async def _plan_strategy(self, 
                           multimodal_query: MultimodalQuery, 
                           decomposition: Dict[str, Any]) -> RetrievalStrategy:
        """策略规划"""
        
        sub_queries = decomposition.get("sub_queries", [])
        
        # 确定查询类型
        query_types = []
        for sub_query in sub_queries:
            query_type = QueryType(sub_query["type"])
            if query_type not in query_types:
                query_types.append(query_type)
        
        # 确定是否需要并行执行
        parallel_execution = len(query_types) > 1
        
        # 确定优先级顺序
        priority_order = [
            sub_query["description"] 
            for sub_query in sorted(sub_queries, key=lambda x: x.get("priority", 5))
        ]
        
        # 确定是否需要融合
        fusion_required = len(query_types) > 1 or multimodal_query.get_modality_count() > 1
        
        return RetrievalStrategy(
            query_types=query_types,
            parallel_execution=parallel_execution,
            priority_order=priority_order,
            fusion_required=fusion_required,
            confidence_threshold=0.6,
            max_results_per_query=20
        )
    
    async def _execute_retrieval_strategy(self, 
                                        strategy: RetrievalStrategy, 
                                        multimodal_query: MultimodalQuery) -> List[Dict[str, Any]]:
        """执行检索策略"""
        
        if not self.query_agents:
            raise Exception("没有可用的查询智能体")
        
        # 记录活跃查询
        self.active_queries[multimodal_query.query_id] = {
            "strategy": strategy,
            "query": multimodal_query,
            "responses": [],
            "start_time": datetime.now()
        }
        
        try:
            if strategy.parallel_execution:
                # 并行执行多个查询
                results = await self._execute_parallel_queries(strategy, multimodal_query)
            else:
                # 顺序执行查询
                results = await self._execute_sequential_queries(strategy, multimodal_query)
            
            # 如果需要融合且有融合智能体
            if strategy.should_use_fusion() and self.fusion_agent and len(results) > 1:
                results = await self._request_fusion(results, multimodal_query)
            
            return results
            
        finally:
            # 清理活跃查询
            if multimodal_query.query_id in self.active_queries:
                del self.active_queries[multimodal_query.query_id]
    
    async def _execute_parallel_queries(self, 
                                      strategy: RetrievalStrategy, 
                                      multimodal_query: MultimodalQuery) -> List[Dict[str, Any]]:
        """并行执行查询"""
        
        tasks = []
        for query_type in strategy.query_types:
            task = self._create_query_task(query_type, multimodal_query, strategy)
            tasks.append(task)
        
        # 并行执行所有查询任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤成功的结果
        valid_results = []
        for result in results:
            if isinstance(result, list):
                valid_results.extend(result)
            elif not isinstance(result, Exception):
                valid_results.append(result)
        
        return valid_results
    
    async def _execute_sequential_queries(self, 
                                        strategy: RetrievalStrategy, 
                                        multimodal_query: MultimodalQuery) -> List[Dict[str, Any]]:
        """顺序执行查询"""
        
        all_results = []
        for query_type in strategy.query_types:
            try:
                results = await self._create_query_task(query_type, multimodal_query, strategy)
                if results:
                    all_results.extend(results)
            except Exception as e:
                print(f"查询执行失败 {query_type}: {e}")
                continue
        
        return all_results
    
    async def _create_query_task(self,
                               query_type: QueryType,
                               multimodal_query: MultimodalQuery,
                               strategy: RetrievalStrategy,
                               decomposition_result: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """创建查询任务"""

        # 构建查询参数
        query_params = {
            "text_query": multimodal_query.text_query,
            "filters": multimodal_query.filters,
            "top_k": strategy.max_results_per_query,
            "similarity_threshold": strategy.confidence_threshold
        }

        # 如果有图像数据，添加到参数中
        if multimodal_query.has_image():
            query_params["image_embedding"] = multimodal_query.image_data
            query_params["embedding_type"] = "shape_embedding"
        elif multimodal_query.has_text():
            query_params["embedding_type"] = "description_embedding"

        # 如果有分解结果，添加详细信息
        if decomposition_result:
            query_params.update({
                "sub_queries": decomposition_result.get("sub_queries", []),
                "execution_strategy": decomposition_result.get("execution_strategy", {}),
                "query_intent": decomposition_result.get("intent", multimodal_query.query_intent),
                "input_modality": decomposition_result.get("input_modality", "文本")
            })

        # 创建任务
        task = {
            "query_id": multimodal_query.query_id,
            "query_type": query_type.value,
            "query_params": query_params
        }

        # 这里应该向多模态查询智能体发送消息
        # 为了演示，返回模拟结果
        return [
            {
                "id": f"result_{query_type.value}_{i}",
                "type": query_type.value,
                "confidence": 0.8,
                "data": {"mock": "data", "query_type": query_type.value}
            }
            for i in range(3)
        ]
    
    async def _request_fusion(self, 
                            results: List[Dict[str, Any]], 
                            multimodal_query: MultimodalQuery) -> List[Dict[str, Any]]:
        """请求融合智能体处理结果"""
        
        # 这里应该向融合智能体发送消息
        # 为了简化，这里返回原始结果
        return results
