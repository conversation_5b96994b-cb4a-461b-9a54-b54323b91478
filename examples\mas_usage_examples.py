"""
多智能体系统使用示例
演示如何使用CAD知识图谱多智能体系统进行各种查询
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.agent import MultiAgentSystem, MultimodalQuery, RetrievalStrategy, QueryType
import numpy as np


async def example_1_simple_text_query():
    """示例1: 简单文本查询"""
    print("=" * 50)
    print("示例1: 简单文本查询")
    print("=" * 50)
    
    # 创建多智能体系统
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 执行简单文本查询
        result = await mas.process_user_query("查找所有的发动机装配体")
        
        print(f"查询成功: {result['success']}")
        print(f"查询ID: {result.get('query_id', 'N/A')}")
        print(f"执行时间: {result.get('metadata', {}).get('execution_time', 0):.2f}秒")
        print(f"结果数量: {result.get('metadata', {}).get('result_count', 0)}")
        
        # 显示前几个结果
        results = result.get('results', [])
        for i, res in enumerate(results[:3]):
            print(f"\n结果 {i+1}:")
            print(f"  ID: {res.get('id', 'N/A')}")
            print(f"  类型: {res.get('type', 'N/A')}")
            print(f"  名称: {res.get('data', {}).get('name', 'N/A')}")
            print(f"  置信度: {res.get('confidence_score', 0):.2f}")
    
    finally:
        await mas.stop()


async def example_2_multimodal_query():
    """示例2: 多模态查询"""
    print("\n" + "=" * 50)
    print("示例2: 多模态查询")
    print("=" * 50)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建多模态查询对象
        multimodal_query = MultimodalQuery(
            text_query="查找铝合金材料的零件",
            filters={
                "material": "aluminum",
                "category": "mechanical"
            },
            query_intent="寻找特定材料的机械零件"
        )
        
        result = await mas.process_multimodal_query_advanced(multimodal_query)
        
        print(f"查询成功: {result['success']}")
        print(f"查询ID: {result['query_id']}")
        print(f"执行时间: {result['execution_time']:.2f}秒")
        print(f"使用策略: {result['strategy_used']}")
        
        # 显示结果
        results = result.get('results', [])
        print(f"\n找到 {len(results)} 个结果:")
        for i, res in enumerate(results[:5]):
            print(f"\n结果 {i+1}:")
            print(f"  ID: {res.get('id', 'N/A')}")
            print(f"  类型: {res.get('type', 'N/A')}")
            data = res.get('data', {})
            print(f"  名称: {data.get('name', 'N/A')}")
            print(f"  材料: {data.get('material', 'N/A')}")
            print(f"  质量: {data.get('mass', 'N/A')}")
    
    finally:
        await mas.stop()


async def example_3_custom_strategy():
    """示例3: 自定义检索策略"""
    print("\n" + "=" * 50)
    print("示例3: 自定义检索策略")
    print("=" * 50)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建自定义检索策略
        custom_strategy = RetrievalStrategy(
            query_types=[QueryType.METADATA, QueryType.STRUCTURE],
            parallel_execution=True,
            fusion_required=True,
            confidence_threshold=0.6,
            max_results_per_query=10
        )
        
        # 创建查询
        query = MultimodalQuery(
            text_query="查找包含螺栓特征的零件",
            filters={"type": "fastener"}
        )
        
        result = await mas.process_multimodal_query_advanced(query, custom_strategy)
        
        print(f"查询成功: {result['success']}")
        print(f"执行时间: {result['execution_time']:.2f}秒")
        print(f"自定义策略: {custom_strategy.query_types}")
        
        results = result.get('results', [])
        print(f"\n使用自定义策略找到 {len(results)} 个结果")
    
    finally:
        await mas.stop()


async def example_4_parallel_queries():
    """示例4: 并行查询"""
    print("\n" + "=" * 50)
    print("示例4: 并行查询")
    print("=" * 50)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建多个查询
        queries = [
            MultimodalQuery(text_query="查找装配体", filters={"category": "automotive"}),
            MultimodalQuery(text_query="查找零件", filters={"material": "steel"}),
            MultimodalQuery(text_query="查找特征", filters={"type": "hole"})
        ]
        
        # 并行执行查询
        results = await mas.execute_parallel_queries(queries, max_concurrent=2)
        
        print(f"并行执行了 {len(queries)} 个查询")
        
        for i, result in enumerate(results):
            print(f"\n查询 {i+1} 结果:")
            print(f"  成功: {result['success']}")
            if result['success']:
                print(f"  执行时间: {result.get('execution_time', 0):.2f}秒")
                print(f"  结果数量: {len(result.get('results', []))}")
            else:
                print(f"  错误: {result.get('error', 'N/A')}")
    
    finally:
        await mas.stop()


async def example_5_vector_similarity_query():
    """示例5: 向量相似性查询（模拟）"""
    print("\n" + "=" * 50)
    print("示例5: 向量相似性查询")
    print("=" * 50)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 模拟图像特征向量（实际应用中应该从图像提取）
        mock_image_embedding = np.random.rand(768).tolist()
        
        # 创建包含图像数据的查询
        query = MultimodalQuery(
            text_query="查找相似形状的零件",
            image_data=mock_image_embedding,  # 在实际应用中这应该是图像字节数据
            filters={"type": "Part"}
        )
        
        result = await mas.process_multimodal_query_advanced(query)
        
        print(f"查询成功: {result['success']}")
        print(f"执行时间: {result['execution_time']:.2f}秒")
        
        if result['success']:
            results = result.get('results', [])
            print(f"\n基于形状相似性找到 {len(results)} 个结果")
            
            for i, res in enumerate(results[:3]):
                print(f"\n相似结果 {i+1}:")
                print(f"  ID: {res.get('id', 'N/A')}")
                print(f"  相似度: {res.get('confidence_score', 0):.3f}")
                data = res.get('data', {})
                print(f"  名称: {data.get('name', 'N/A')}")
        else:
            print(f"查询失败: {result.get('error', 'N/A')}")
    
    finally:
        await mas.stop()


async def example_6_system_status():
    """示例6: 系统状态监控"""
    print("\n" + "=" * 50)
    print("示例6: 系统状态监控")
    print("=" * 50)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 获取系统状态
        status = mas.get_system_status()
        
        print("系统状态:")
        print(f"  运行状态: {status['is_running']}")
        print(f"  活跃会话数: {status['active_sessions']}")
        
        print("\n智能体状态:")
        for agent_id, agent_status in status['agents'].items():
            print(f"  {agent_status['name']} ({agent_id}):")
            print(f"    状态: {agent_status['status']}")
            print(f"    消息数量: {agent_status['message_count']}")
        
        # 执行一个查询来创建会话
        await mas.process_user_query("测试查询", session_id="test_session")
        
        # 再次检查状态
        updated_status = mas.get_system_status()
        print(f"\n执行查询后的活跃会话数: {updated_status['active_sessions']}")
        
        # 获取会话信息
        session_info = mas.get_session_info("test_session")
        if session_info:
            print(f"\n会话信息:")
            print(f"  会话ID: {session_info['session_id']}")
            print(f"  状态: {session_info['status']}")
            print(f"  开始时间: {session_info['start_time']}")
    
    finally:
        await mas.stop()


async def main():
    """运行所有示例"""
    print("CAD知识图谱多智能体系统使用示例")
    print("=" * 60)
    
    examples = [
        example_1_simple_text_query,
        example_2_multimodal_query,
        example_3_custom_strategy,
        example_4_parallel_queries,
        example_5_vector_similarity_query,
        example_6_system_status
    ]
    
    for example in examples:
        try:
            await example()
        except Exception as e:
            print(f"\n示例执行失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 等待一下再执行下一个示例
        await asyncio.sleep(1)
    
    print("\n" + "=" * 60)
    print("所有示例执行完成")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
