import base64
import json
from typing import List, Dict, Any
import requests


class MultiProviderLLM:
    """
    统一封装 OpenAI / Azure OpenAI / Ollama / Gemini / 其他 REST 模型。
    """

    def __init__(
        self,
        provider: str,
        api_url: str,
        api_key: str,
        **kwargs,
    ):
        """
        Parameters
        ----------
        provider : 供应商名称（大小写均可，如 'openai'、'azure'、'ollama'、'gemini'）
        api_url  : 基础 URL（到组织资源 / 本地端口为止，末尾不带斜杠）
        api_key  : API 密钥或 Token
        kwargs   : 供应商专属可选参数，例如
                   - deployment (Azure OpenAI)
                   - api_version (Azure OpenAI，默认 2024-02-15-preview)
                   - model (Gemini / OpenAI / Ollama 默认模型)
        """
        self.provider = provider.lower()
        self.base_url = api_url.rstrip("/")
        self.api_key = api_key
        self.opts = kwargs

        # 预生成 endpoint，后面直接用
        self.endpoint = self._build_endpoint()

    # ------------------------------------------------------------------ #
    # 对外方法
    # ------------------------------------------------------------------ #
    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """纯文本对话（messages 与 OpenAI 格式兼容）。"""
        payload = self._build_payload(messages=messages, images=None, **kwargs)
        resp = requests.post(
            self.endpoint,
            headers=self._build_headers(),
            json=payload,
            stream=False,
            timeout=kwargs.get("timeout", 60),
        )
        resp.raise_for_status()
        return self._extract_text(resp.json())

    def chat_with_images(
        self,
        messages: List[Dict[str, str]],
        image_paths: List[str],
        **kwargs,
    ) -> str:
        """
        多模态对话（目前主要支持 Gemini）。
        `image_paths` 里放本地图片路径；函数会自动转 base64。
        """
        images_b64 = [self._img2b64(p) for p in image_paths]
        payload = self._build_payload(messages=messages, images=images_b64, **kwargs)
        resp = requests.post(
            self.endpoint,
            headers=self._build_headers(),
            json=payload,
            stream=False,
            timeout=kwargs.get("timeout", 90),
        )
        resp.raise_for_status()
        return self._extract_text(resp.json())

    # ------------------------------------------------------------------ #
    # 内部辅助
    # ------------------------------------------------------------------ #
    def _build_endpoint(self) -> str:
        """根据供应商拼接最终 endpoint。"""
        if self.provider == "openai":
            return f"{self.base_url}/v1/chat/completions"
        if self.provider in ("azure", "azure openai"):
            deployment = self.opts.get("deployment")
            if not deployment:
                raise ValueError("Azure OpenAI 需要指定 deployment=<your-deployment-name>")
            api_ver = self.opts.get("api_version", "2024-02-15-preview")
            return f"{self.base_url}/openai/deployments/{deployment}/chat/completions?api-version={api_ver}"
        if self.provider == "ollama":
            return f"{self.base_url}/api/chat"
        if self.provider == "gemini":
            model = self.opts.get("model", "gemini-pro")
            return f"{self.base_url}/v1beta/models/{model}:generateContent?key={self.api_key}"
        # 默认：直接用传入的 base_url
        return self.base_url

    def _build_headers(self) -> Dict[str, str]:
        """根据供应商生成请求头。"""
        if self.provider in ("openai", ):
            return {"Authorization": f"Bearer {self.api_key}"}
        if self.provider in ("azure", "azure openai"):
            return {"api-key": self.api_key}
        if self.provider == "gemini":
            # Gemini 不再需要 Authorization，只需 Content-Type
            return {"Content-Type": "application/json"}
        # Ollama 或其他本地 / 私有服务多数无鉴权
        return {"Content-Type": "application/json"}

    def _build_payload(
        self,
        messages: List[Dict[str, str]],
        images: List[str] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """根据供应商把用户 messages → 对应 JSON 结构。"""
        model = kwargs.get("model", self.opts.get("model"))
        if self.provider in ("openai", "azure", "ollama"):
            payload = {"messages": messages}
            if model:
                payload["model"] = model
            payload.update(kwargs)
            return payload

        if self.provider == "gemini":
            # Gemini 文档：每条 message 要折成 parts; role 目前由顺序隐含
            contents = []
            for m in messages:
                part_list = [{"text": m["content"]}]
                contents.append({"role": m.get("role", "user"), "parts": part_list})

            # 如果有图片，把最后一条 user prompt 改成 multimodal
            if images:
                # Gemini: 一张图 -> part = {"inline_data": {"mime_type": "image/jpeg", "data": b64}}
                img_parts = [
                    {"inline_data": {"mime_type": "image/png", "data": b64}}
                    for b64 in images
                ]
                contents.append(
                    {
                        "role": "user",
                        "parts": img_parts,
                    }
                )
            return {"contents": contents, **kwargs}

        # 其他 REST 服务：默认直接把 messages 贴进去
        return {"messages": messages, **kwargs}

    @staticmethod
    def _extract_text(resp_json: Dict[str, Any]) -> str:
        """根据供应商格式提取回答文本。"""
        # OpenAI / Azure / Ollama
        if "choices" in resp_json:
            return resp_json["choices"][0]["message"]["content"]

        # Gemini
        if "candidates" in resp_json:
            parts = resp_json["candidates"][0]["content"]["parts"]
            return "".join(p.get("text", "") for p in parts)

        # 兜底
        return json.dumps(resp_json, ensure_ascii=False, indent=2)

    @staticmethod
    def _img2b64(path: str) -> str:
        with open(path, "rb") as f:
            return base64.b64encode(f.read()).decode("ascii")
