"""
LLM配置管理
"""

import os
from typing import Dict, Any
from ..models.LLM import MultiProviderLLM


class LLMConfig:
    """LLM配置管理类"""
    
    @staticmethod
    def get_default_config() -> Dict[str, Any]:
        """获取默认LLM配置"""
        return {
            "provider": os.getenv("LLM_PROVIDER", "openai"),
            "api_url": os.getenv("LLM_API_URL", "https://api.openai.com"),
            "api_key": os.getenv("LLM_API_KEY", "your-api-key"),
            "model": os.getenv("LLM_MODEL", "gpt-3.5-turbo"),
            "temperature": float(os.getenv("LLM_TEMPERATURE", "0.1")),
            "timeout": int(os.getenv("LLM_TIMEOUT", "60"))
        }
    
    @staticmethod
    def create_llm(config: Dict[str, Any] = None) -> MultiProviderLLM:
        """创建LLM实例"""
        if config is None:
            config = LLMConfig.get_default_config()
        
        return MultiProviderLLM(
            provider=config["provider"],
            api_url=config["api_url"],
            api_key=config["api_key"],
            model=config["model"]
        )
    
    @staticmethod
    def create_default_llm() -> MultiProviderLLM:
        """创建默认LLM实例"""
        return LLMConfig.create_llm()


# 便捷函数
def get_default_llm() -> MultiProviderLLM:
    """获取默认LLM实例"""
    return LLMConfig.create_default_llm()
