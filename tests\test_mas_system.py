"""
多智能体系统测试
"""

import pytest
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.agent import (
    MultiAgentSystem, MultimodalQuery, RetrievalStrategy, QueryType,
    OrchestratorAgent, MultimodalQueryAgent, FusionAgent
)


class TestMultiAgentSystem:
    """多智能体系统测试类"""
    
    @pytest.fixture
    async def mas_system(self):
        """创建测试用的多智能体系统"""
        mas = MultiAgentSystem()
        await mas.start()
        yield mas
        await mas.stop()
    
    @pytest.mark.asyncio
    async def test_system_initialization(self):
        """测试系统初始化"""
        mas = MultiAgentSystem()
        
        # 检查智能体是否正确初始化
        assert mas.orchestrator is not None
        assert mas.query_agent is not None
        assert mas.fusion_agent is not None
        
        # 检查智能体注册
        assert len(mas.agents) == 3
        assert mas.orchestrator.agent_id in mas.agents
        assert mas.query_agent.agent_id in mas.agents
        assert mas.fusion_agent.agent_id in mas.agents
    
    @pytest.mark.asyncio
    async def test_system_start_stop(self, mas_system):
        """测试系统启动和停止"""
        assert mas_system.is_running is True
        
        await mas_system.stop()
        assert mas_system.is_running is False
    
    @pytest.mark.asyncio
    async def test_simple_text_query(self, mas_system):
        """测试简单文本查询"""
        result = await mas_system.process_user_query("查找装配体")
        
        assert isinstance(result, dict)
        assert "success" in result
        assert "query_id" in result
        assert "results" in result
        assert "metadata" in result
    
    @pytest.mark.asyncio
    async def test_multimodal_query_object(self, mas_system):
        """测试多模态查询对象"""
        query = MultimodalQuery(
            text_query="测试查询",
            filters={"type": "Assembly"}
        )
        
        result = await mas_system.process_multimodal_query_advanced(query)
        
        assert isinstance(result, dict)
        assert "success" in result
        assert "query_id" in result
        assert result["query_id"] == query.query_id
    
    @pytest.mark.asyncio
    async def test_custom_strategy(self, mas_system):
        """测试自定义检索策略"""
        strategy = RetrievalStrategy(
            query_types=[QueryType.METADATA],
            parallel_execution=False,
            max_results_per_query=5
        )
        
        query = MultimodalQuery(text_query="测试查询")
        
        result = await mas_system.process_multimodal_query_advanced(query, strategy)
        
        assert isinstance(result, dict)
        assert "strategy_used" in result
        assert result["strategy_used"] == strategy.__dict__
    
    @pytest.mark.asyncio
    async def test_parallel_queries(self, mas_system):
        """测试并行查询"""
        queries = [
            MultimodalQuery(text_query="查询1"),
            MultimodalQuery(text_query="查询2"),
            MultimodalQuery(text_query="查询3")
        ]
        
        results = await mas_system.execute_parallel_queries(queries, max_concurrent=2)
        
        assert len(results) == 3
        for result in results:
            assert isinstance(result, dict)
            assert "success" in result
            assert "query_id" in result
    
    @pytest.mark.asyncio
    async def test_system_status(self, mas_system):
        """测试系统状态"""
        status = mas_system.get_system_status()
        
        assert isinstance(status, dict)
        assert "is_running" in status
        assert "active_sessions" in status
        assert "agents" in status
        assert status["is_running"] is True
        assert len(status["agents"]) == 3
    
    @pytest.mark.asyncio
    async def test_session_management(self, mas_system):
        """测试会话管理"""
        # 执行查询创建会话
        result = await mas_system.process_user_query("测试", session_id="test_session")
        
        assert result["session_id"] == "test_session"
        
        # 检查会话信息
        session_info = mas_system.get_session_info("test_session")
        assert session_info is not None
        assert session_info["session_id"] == "test_session"
        assert "start_time" in session_info
        assert "status" in session_info


class TestIndividualAgents:
    """单个智能体测试类"""
    
    def test_orchestrator_agent_initialization(self):
        """测试编排智能体初始化"""
        agent = OrchestratorAgent()
        
        assert agent.agent_id == "orchestrator"
        assert agent.name == "编排智能体"
        assert agent.status == "initialized"
        assert len(agent.query_agents) == 0
        assert agent.fusion_agent is None
    
    def test_multimodal_query_agent_initialization(self):
        """测试多模态查询智能体初始化"""
        # 注意：这个测试可能需要数据库连接，在实际环境中可能会失败
        try:
            agent = MultimodalQueryAgent()
            assert agent.agent_id == "multimodal_query"
            assert agent.name == "多模态查询智能体"
        except Exception as e:
            # 如果数据库连接失败，跳过测试
            pytest.skip(f"数据库连接失败，跳过测试: {e}")
    
    def test_fusion_agent_initialization(self):
        """测试融合智能体初始化"""
        agent = FusionAgent()
        
        assert agent.agent_id == "fusion"
        assert agent.name == "融合智能体"
        assert agent.status == "initialized"
        assert len(agent.fusion_strategies) > 0


class TestDataStructures:
    """数据结构测试类"""
    
    def test_multimodal_query_creation(self):
        """测试多模态查询创建"""
        query = MultimodalQuery(
            text_query="测试查询",
            filters={"type": "Assembly"},
            query_intent="测试意图"
        )
        
        assert query.text_query == "测试查询"
        assert query.filters == {"type": "Assembly"}
        assert query.query_intent == "测试意图"
        assert query.has_text() is True
        assert query.has_image() is False
        assert query.get_modality_count() == 1
    
    def test_retrieval_strategy_creation(self):
        """测试检索策略创建"""
        strategy = RetrievalStrategy(
            query_types=[QueryType.METADATA, QueryType.STRUCTURE],
            parallel_execution=True,
            fusion_required=True
        )
        
        assert len(strategy.query_types) == 2
        assert QueryType.METADATA in strategy.query_types
        assert QueryType.STRUCTURE in strategy.query_types
        assert strategy.parallel_execution is True
        assert strategy.should_use_fusion() is True
    
    def test_query_type_enum(self):
        """测试查询类型枚举"""
        assert QueryType.METADATA.value == "metadata"
        assert QueryType.STRUCTURE.value == "structure"
        assert QueryType.VECTOR_SIMILARITY.value == "vector_similarity"
        assert QueryType.HYBRID.value == "hybrid"


# 运行测试的便捷函数
def run_tests():
    """运行所有测试"""
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_tests()
