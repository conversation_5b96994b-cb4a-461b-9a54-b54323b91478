"""
智能体基类和核心数据结构定义
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union
from enum import Enum
import uuid
import json
from datetime import datetime


class MessageType(Enum):
    """消息类型枚举"""
    QUERY = "query"
    RESPONSE = "response"
    COMMAND = "command"
    STATUS = "status"
    ERROR = "error"


class QueryType(Enum):
    """查询类型枚举"""
    METADATA = "metadata"
    STRUCTURE = "structure"
    VECTOR_SIMILARITY = "vector_similarity"
    HYBRID = "hybrid"


@dataclass
class AgentMessage:
    """智能体间通信消息"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender: str = ""
    receiver: str = ""
    message_type: MessageType = MessageType.QUERY
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        data = {
            'id': self.id,
            'sender': self.sender,
            'receiver': self.receiver,
            'message_type': self.message_type.value,
            'content': self.content,
            'timestamp': self.timestamp.isoformat()
        }
        return json.dumps(data, ensure_ascii=False, indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'AgentMessage':
        """从JSON字符串创建消息"""
        data = json.loads(json_str)
        return cls(
            id=data['id'],
            sender=data['sender'],
            receiver=data['receiver'],
            message_type=MessageType(data['message_type']),
            content=data['content'],
            timestamp=datetime.fromisoformat(data['timestamp'])
        )


@dataclass
class QueryResult:
    """查询结果数据结构"""
    query_id: str
    query_type: QueryType
    results: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    confidence_score: float = 0.0
    execution_time: float = 0.0
    error: Optional[str] = None
    
    def is_successful(self) -> bool:
        """检查查询是否成功"""
        return self.error is None
    
    def get_result_count(self) -> int:
        """获取结果数量"""
        return len(self.results)


class BaseAgent(ABC):
    """智能体基类"""
    
    def __init__(self, agent_id: str, name: str):
        self.agent_id = agent_id
        self.name = name
        self.status = "initialized"
        self.message_history: List[AgentMessage] = []
        
    @abstractmethod
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """
        处理接收到的消息
        
        Args:
            message: 接收到的消息
            
        Returns:
            AgentMessage: 响应消息
        """
        pass
    
    @abstractmethod
    async def execute_task(self, task: Dict[str, Any]) -> QueryResult:
        """
        执行具体任务
        
        Args:
            task: 任务描述
            
        Returns:
            QueryResult: 执行结果
        """
        pass
    
    def create_message(self, 
                      receiver: str, 
                      message_type: MessageType, 
                      content: Dict[str, Any]) -> AgentMessage:
        """创建消息"""
        return AgentMessage(
            sender=self.agent_id,
            receiver=receiver,
            message_type=message_type,
            content=content
        )
    
    def log_message(self, message: AgentMessage):
        """记录消息历史"""
        self.message_history.append(message)
        
    def get_status(self) -> Dict[str, Any]:
        """获取智能体状态"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status,
            "message_count": len(self.message_history)
        }


@dataclass
class MultimodalQuery:
    """多模态查询数据结构"""
    query_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    text_query: Optional[str] = None
    image_data: Optional[bytes] = None
    sketch_data: Optional[Dict[str, Any]] = None
    cad_fragment: Optional[Dict[str, Any]] = None
    filters: Dict[str, Any] = field(default_factory=dict)
    query_intent: Optional[str] = None
    
    def has_text(self) -> bool:
        """检查是否包含文本查询"""
        return self.text_query is not None and len(self.text_query.strip()) > 0
    
    def has_image(self) -> bool:
        """检查是否包含图像数据"""
        return self.image_data is not None
    
    def has_sketch(self) -> bool:
        """检查是否包含草图数据"""
        return self.sketch_data is not None
    
    def has_cad_fragment(self) -> bool:
        """检查是否包含CAD片段"""
        return self.cad_fragment is not None
    
    def get_modality_count(self) -> int:
        """获取模态数量"""
        count = 0
        if self.has_text():
            count += 1
        if self.has_image():
            count += 1
        if self.has_sketch():
            count += 1
        if self.has_cad_fragment():
            count += 1
        return count


@dataclass
class RetrievalStrategy:
    """检索策略数据结构"""
    strategy_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    query_types: List[QueryType] = field(default_factory=list)
    parallel_execution: bool = True
    priority_order: List[str] = field(default_factory=list)
    fusion_required: bool = False
    confidence_threshold: float = 0.5
    max_results_per_query: int = 50
    
    def should_use_fusion(self) -> bool:
        """判断是否需要使用融合智能体"""
        return self.fusion_required or len(self.query_types) > 1
