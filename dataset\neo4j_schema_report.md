# Neo4j 数据库 Schema 报告

## 1. 节点标签及其属性

### 标签: Assembly (数量: 11)

属性列表:

- area
- description
- category
- density
- id
- industry
- mass
- name
- shape_embedding
- description_embedding
- volume

### 标签: Feature (数量: 360)

属性列表:

- diameter
- id
- length
- name
- type

### 标签: Part (数量: 289)

属性列表:

- area
- density
- id
- mass
- material
- name
- shape_embedding
- volume
- description
- description_embedding

### 标签: SubAssembly (数量: 51)

属性列表:

- area
- density
- id
- mass
- name
- volume


## 2. 关系类型及其属性

### 关系类型: hasFeature (数量: 360)

- 无属性或数据不足

存在的关系模式:

- (Part)-[:hasFeature]->(Feature) (数量: 360)

### 关系类型: hasPart (数量: 289)

- 无属性或数据不足

存在的关系模式:

- (SubAssembly)-[:hasPart]->(Part) (数量: 136)
- (Assembly)-[:hasPart]->(Part) (数量: 153)

### 关系类型: hasSubAssembly (数量: 51)

- 无属性或数据不足

存在的关系模式:

- (Assembly)-[:hasSubAssembly]->(SubAssembly) (数量: 36)
- (SubAssembly)-[:hasSubAssembly]->(SubAssembly) (数量: 15)

## 3. 索引信息

| 名称                       | 类型   | 标签/关系类型 | 属性            |
| -------------------------- | ------ | ------------- | --------------- |
| assembly_vectors           | VECTOR | Assembly      | test_embedding  |
| index_343aff4e             | LOOKUP | None          | None            |
| index_f7700477             | LOOKUP | None          | None            |
| part_shape_embedding_index | VECTOR | Part          | shape_embedding |
| shape_embedding_index      | VECTOR | Assembly      | shape_embedding |

## 4. 约束信息

未发现任何约束。

## 5. 数据库统计摘要

- 总节点标签数: 4
- 总关系类型数: 3
- 总索引数: 5
- 总约束数: 0
- 总节点数: 711
- 总关系数: 700
