import os
import sys
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.fusion360_extractor import Fusion360Extractor
from src.utils.llm_utils import call_llm, call_mllm

mllm_system_prompt = """
    你是一名兼具机械设计与技术写作背景的资深工程师。请根据提供的装配渲染图和结构化信息，为非专业读者生成一段准确、通俗、连贯的中文文字说明，突出整体外形、主要组成、功能用途和材料特色。"
"""


def test_fusion360_extractor(json_path):
    extractor = Fusion360Extractor(json_path, extract_shape_embedding=False)
    cad_model = extractor.convert()
    print(cad_model.to_json())


def describe_assembly(json_path: str, image_path: str):
    """
    通过cad_model.to_json()的json信息和渲染图片，得到装配体的文本描述
    """
    extractor = Fusion360Extractor(json_path, extract_shape_embedding=False)
    cad_model = extractor.convert()
    cad_model_json = cad_model.to_json()

    mllm_prompt = f"""
**图片**：<IMAGE>
**装配体结构化信息**：
{cad_model_json}

**命名清洗规则**
- 忽略“Untitled”“Component+数字”“Body+数字”等无意义名称。
- 必要时基于图像与上下文推断更常见的称谓（如“机身”“轮组”等）。

**输出格式要求**
请以如下JSON格式输出：
```
{{
  "assembly_name": "（推断出的装配体名称，简洁明了）",
  "description": "（整体概述，2-3句，突出用途和外观）",
  "components": [
    {{"name": "部件名称", "feature": "简要特征/作用"}},
    ...
  ],
  "materials and techniques": "（如可判断，简要描述主要材料和工艺亮点）"
}}
```
注意：所有字段均需填写，components为部件列表，feature为该部件的简要说明。
风格：简洁专业但易懂，避免长复句，适当使用空间位置词。
"""

    description = call_mllm(prompt=mllm_prompt, system_prompt=mllm_system_prompt,image_path=image_path)
    return description


if __name__ == "__main__":
    json_path = os.path.join("test/41032_ed481084", "assembly.json")
    image_path = "test/41032_ed481084/assembly.png"

    # 输出装配体描述
    description = describe_assembly(json_path, image_path)
    print("装配体描述：")
    print(description)
