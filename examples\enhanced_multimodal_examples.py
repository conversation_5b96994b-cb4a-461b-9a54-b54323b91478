"""
增强的多模态查询示例
展示图片和文字两种输入模态的处理能力
"""

import asyncio
import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.agent import MultiAgentSystem, MultimodalQuery, RetrievalStrategy, QueryType


async def example_image_input_query():
    """示例1: 图片输入查询（形状相似性）"""
    print("=" * 60)
    print("示例1: 图片输入查询 - 形状相似性检索")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 模拟图片输入（实际应用中应该是图片的特征向量）
        mock_image_embedding = np.random.rand(768).tolist()
        
        # 创建图片查询
        image_query = MultimodalQuery(
            text_query="查找相似形状的零件",  # 可选的文本描述
            image_data=mock_image_embedding,  # 图片特征向量
            filters={"type": "Part"},  # 可选过滤条件
            query_intent="基于图片查找形状相似的零件"
        )
        
        print(f"输入模态: 图片 + 文字")
        print(f"查询意图: {image_query.query_intent}")
        print(f"预期查询类型: vector_similarity (shape_embedding)")
        
        result = await mas.process_multimodal_query_advanced(image_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
        
        # 显示前几个结果
        for i, res in enumerate(result.get('results', [])[:3]):
            print(f"\n  相似结果 {i+1}:")
            print(f"    ID: {res.get('id', 'N/A')}")
            print(f"    类型: {res.get('type', 'N/A')}")
            print(f"    相似度: {res.get('confidence_score', 0):.3f}")
    
    finally:
        await mas.stop()


async def example_text_metadata_query():
    """示例2: 文字输入 - 元数据查询"""
    print("\n" + "=" * 60)
    print("示例2: 文字输入 - 元数据查询")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建元数据查询
        metadata_query = MultimodalQuery(
            text_query="查找铝合金材料的汽车零件",
            filters={"category": "automotive"},
            query_intent="基于材料和类别查找零件"
        )
        
        print(f"输入模态: 文字")
        print(f"查询文本: {metadata_query.text_query}")
        print(f"预期查询类型: metadata")
        print(f"预期提取属性: material=aluminum, category=automotive")
        
        result = await mas.process_multimodal_query_advanced(metadata_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
        
        # 显示结果
        for i, res in enumerate(result.get('results', [])[:3]):
            data = res.get('data', {})
            print(f"\n  结果 {i+1}:")
            print(f"    名称: {data.get('name', 'N/A')}")
            print(f"    材料: {data.get('material', 'N/A')}")
            print(f"    类别: {data.get('category', 'N/A')}")
    
    finally:
        await mas.stop()


async def example_text_structure_query():
    """示例3: 文字输入 - 结构查询"""
    print("\n" + "=" * 60)
    print("示例3: 文字输入 - 结构查询")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建结构查询
        structure_query = MultimodalQuery(
            text_query="查找包含螺栓特征的装配体结构",
            query_intent="基于结构关系查找装配体"
        )
        
        print(f"输入模态: 文字")
        print(f"查询文本: {structure_query.text_query}")
        print(f"预期查询类型: structure")
        print(f"预期关系: Assembly -> Part -> Feature")
        
        result = await mas.process_multimodal_query_advanced(structure_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
    
    finally:
        await mas.stop()


async def example_text_semantic_query():
    """示例4: 文字输入 - 语义相似性查询"""
    print("\n" + "=" * 60)
    print("示例4: 文字输入 - 语义相似性查询")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建语义查询
        semantic_query = MultimodalQuery(
            text_query="查找功能类似发动机的装配体",
            query_intent="基于功能描述查找相似装配体"
        )
        
        print(f"输入模态: 文字")
        print(f"查询文本: {semantic_query.text_query}")
        print(f"预期查询类型: vector_similarity (description_embedding)")
        print(f"预期处理: 文本 -> 嵌入向量 -> 语义相似性搜索")
        
        result = await mas.process_multimodal_query_advanced(semantic_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
    
    finally:
        await mas.stop()


async def example_hybrid_query_with_filter_chain():
    """示例5: 混合查询 - 过滤链"""
    print("\n" + "=" * 60)
    print("示例5: 混合查询 - 过滤链处理")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建复杂的混合查询
        hybrid_query = MultimodalQuery(
            text_query="查找钢材制造的发动机装配体结构",
            filters={"material": "steel"},
            query_intent="先按材料过滤，再查找发动机相关结构"
        )
        
        print(f"输入模态: 文字")
        print(f"查询文本: {hybrid_query.text_query}")
        print(f"预期查询类型: hybrid")
        print(f"预期执行顺序:")
        print(f"  1. metadata查询: material=steel")
        print(f"  2. structure查询: 发动机装配体结构 (使用步骤1结果过滤)")
        print(f"  3. vector_similarity查询: 语义相似性 (使用前序结果过滤)")
        
        result = await mas.process_multimodal_query_advanced(hybrid_query)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  结果数量: {len(result.get('results', []))}")
    
    finally:
        await mas.stop()


async def example_custom_strategy_for_multimodal():
    """示例6: 自定义策略的多模态查询"""
    print("\n" + "=" * 60)
    print("示例6: 自定义策略的多模态查询")
    print("=" * 60)
    
    mas = MultiAgentSystem()
    await mas.start()
    
    try:
        # 创建自定义策略
        custom_strategy = RetrievalStrategy(
            query_types=[QueryType.METADATA, QueryType.VECTOR_SIMILARITY],
            parallel_execution=False,  # 顺序执行
            fusion_required=True,
            confidence_threshold=0.7,
            max_results_per_query=5
        )
        
        # 创建查询
        query = MultimodalQuery(
            text_query="查找铝合金材料且功能类似的零件",
            query_intent="结合材料属性和功能相似性查找"
        )
        
        print(f"输入模态: 文字")
        print(f"自定义策略:")
        print(f"  查询类型: {[qt.value for qt in custom_strategy.query_types]}")
        print(f"  执行方式: {'并行' if custom_strategy.parallel_execution else '顺序'}")
        print(f"  需要融合: {custom_strategy.fusion_required}")
        
        result = await mas.process_multimodal_query_advanced(query, custom_strategy)
        
        print(f"\n查询结果:")
        print(f"  成功: {result['success']}")
        print(f"  执行时间: {result['execution_time']:.2f}秒")
        print(f"  使用策略: 自定义")
        print(f"  结果数量: {len(result.get('results', []))}")
    
    finally:
        await mas.stop()


async def main():
    """运行所有增强的多模态示例"""
    print("增强的多模态查询系统演示")
    print("支持图片和文字两种输入模态")
    print("=" * 60)
    
    examples = [
        example_image_input_query,
        example_text_metadata_query,
        example_text_structure_query,
        example_text_semantic_query,
        example_hybrid_query_with_filter_chain,
        example_custom_strategy_for_multimodal
    ]
    
    for example in examples:
        try:
            await example()
            await asyncio.sleep(1)  # 短暂暂停
        except Exception as e:
            print(f"\n示例执行失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("所有增强示例执行完成")
    print("\n关键特性总结:")
    print("✅ 图片输入 -> shape_embedding相似性检索")
    print("✅ 文字输入 -> 智能查询类型识别")
    print("✅ 元数据查询 -> 属性提取和精确匹配")
    print("✅ 结构查询 -> 图关系遍历")
    print("✅ 语义查询 -> description_embedding相似性")
    print("✅ 混合查询 -> 过滤链和依赖关系")
    print("✅ 自定义策略 -> 灵活的执行控制")


if __name__ == "__main__":
    asyncio.run(main())
