"""
融合智能体 (Fusion Agent)
负责收集、融合、排序和提炼多路径查询结果
"""

import asyncio
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
from collections import defaultdict
from dataclasses import dataclass

from .base_agent import (
    BaseAgent, AgentMessage, QueryResult, MessageType, QueryType,
    MultimodalQuery
)
from ..models.LLM import LLMManager


@dataclass
class FusionResult:
    """融合结果数据结构"""
    entity_id: str
    entity_type: str
    entity_data: Dict[str, Any]
    confidence_score: float
    source_queries: List[str]
    fusion_metadata: Dict[str, Any]


class FusionAgent(BaseAgent):
    """融合智能体 - 智能化融合多路径查询结果"""
    
    def __init__(self, agent_id: str = "fusion", name: str = "融合智能体"):
        super().__init__(agent_id, name)
        self.llm_manager = LLMManager()
        
        # 融合策略配置
        self.fusion_strategies = {
            "weighted_average": self._weighted_average_fusion,
            "max_confidence": self._max_confidence_fusion,
            "consensus_based": self._consensus_based_fusion,
            "llm_guided": self._llm_guided_fusion
        }
        
        # 默认融合参数
        self.default_fusion_params = {
            "strategy": "consensus_based",
            "confidence_threshold": 0.5,
            "max_results": 20,
            "diversity_factor": 0.3,
            "relevance_weight": 0.7,
            "similarity_weight": 0.3
        }
        
        logging.info(f"融合智能体 {self.agent_id} 初始化完成")
    
    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理接收到的消息"""
        self.log_message(message)
        
        if message.message_type == MessageType.COMMAND:
            # 处理融合命令
            return await self._handle_fusion_command(message)
        else:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"不支持的消息类型: {message.message_type}"}
            )
    
    async def execute_task(self, task: Dict[str, Any]) -> QueryResult:
        """执行融合任务"""
        query_id = task.get("query_id", "")
        results_list = task.get("results_list", [])
        fusion_params = {**self.default_fusion_params, **task.get("fusion_params", {})}
        multimodal_query = task.get("multimodal_query")
        
        start_time = datetime.now()
        
        try:
            if not results_list:
                return QueryResult(
                    query_id=query_id,
                    query_type=QueryType.HYBRID,
                    results=[],
                    metadata={"message": "没有结果需要融合"}
                )
            
            # 执行融合过程
            fused_results = await self._execute_fusion_pipeline(
                results_list, fusion_params, multimodal_query
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return QueryResult(
                query_id=query_id,
                query_type=QueryType.HYBRID,
                results=fused_results,
                execution_time=execution_time,
                metadata={
                    "fusion_strategy": fusion_params["strategy"],
                    "original_result_count": sum(len(results) for results in results_list),
                    "fused_result_count": len(fused_results),
                    "fusion_params": fusion_params
                }
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logging.error(f"融合任务执行失败: {e}")
            
            return QueryResult(
                query_id=query_id,
                query_type=QueryType.HYBRID,
                execution_time=execution_time,
                error=str(e)
            )
    
    async def _handle_fusion_command(self, message: AgentMessage) -> AgentMessage:
        """处理融合命令"""
        try:
            command_data = message.content
            task = {
                "query_id": command_data.get("query_id", ""),
                "results_list": command_data.get("results_list", []),
                "fusion_params": command_data.get("fusion_params", {}),
                "multimodal_query": command_data.get("multimodal_query")
            }
            
            result = await self.execute_task(task)
            
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.RESPONSE,
                content={
                    "query_id": result.query_id,
                    "success": result.is_successful(),
                    "fused_results": result.results,
                    "metadata": result.metadata,
                    "error": result.error
                }
            )
            
        except Exception as e:
            return self.create_message(
                receiver=message.sender,
                message_type=MessageType.ERROR,
                content={"error": f"处理融合命令失败: {str(e)}"}
            )
    
    async def _execute_fusion_pipeline(self, 
                                     results_list: List[List[Dict[str, Any]]], 
                                     fusion_params: Dict[str, Any],
                                     multimodal_query: Optional[MultimodalQuery] = None) -> List[Dict[str, Any]]:
        """执行融合流水线"""
        
        # 1. 预处理和标准化结果
        normalized_results = self._normalize_results(results_list)
        
        # 2. 实体对齐和去重
        aligned_entities = self._align_entities(normalized_results)
        
        # 3. 计算融合分数
        fusion_results = await self._calculate_fusion_scores(
            aligned_entities, fusion_params, multimodal_query
        )
        
        # 4. 应用融合策略
        strategy_name = fusion_params.get("strategy", "consensus_based")
        fusion_strategy = self.fusion_strategies.get(strategy_name, self._consensus_based_fusion)
        
        final_results = await fusion_strategy(fusion_results, fusion_params)
        
        # 5. 后处理和排序
        processed_results = self._post_process_results(final_results, fusion_params)
        
        # 6. 应用多样性和相关性平衡
        balanced_results = self._apply_diversity_balance(processed_results, fusion_params)
        
        return balanced_results
    
    def _normalize_results(self, results_list: List[List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """标准化结果格式"""
        
        normalized = []
        
        for query_idx, results in enumerate(results_list):
            for result in results:
                # 提取实体信息
                entity_data = self._extract_entity_data(result)
                if entity_data:
                    normalized_result = {
                        "entity_id": entity_data.get("id", f"unknown_{len(normalized)}"),
                        "entity_type": entity_data.get("type", "unknown"),
                        "entity_data": entity_data,
                        "source_query_index": query_idx,
                        "original_confidence": result.get("confidence", result.get("similarity_score", 0.5)),
                        "source_metadata": result
                    }
                    normalized.append(normalized_result)
        
        return normalized
    
    def _extract_entity_data(self, result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从结果中提取实体数据"""
        
        # 处理不同的结果格式
        if "entity" in result:
            entity = result["entity"]
            entity_type = result.get("types", ["unknown"])[0] if result.get("types") else "unknown"
            return {**entity, "type": entity_type}
        
        elif "assembly" in result:
            return {**result["assembly"], "type": "Assembly"}
        
        elif "part" in result:
            return {**result["part"], "type": "Part"}
        
        elif "feature" in result:
            return {**result["feature"], "type": "Feature"}
        
        elif "id" in result:
            return result
        
        return None
    
    def _align_entities(self, normalized_results: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """实体对齐和分组"""
        
        entity_groups = defaultdict(list)
        
        for result in normalized_results:
            entity_id = result["entity_id"]
            entity_groups[entity_id].append(result)
        
        return dict(entity_groups)
    
    async def _calculate_fusion_scores(self, 
                                     aligned_entities: Dict[str, List[Dict[str, Any]]], 
                                     fusion_params: Dict[str, Any],
                                     multimodal_query: Optional[MultimodalQuery] = None) -> List[FusionResult]:
        """计算融合分数"""
        
        fusion_results = []
        
        for entity_id, entity_instances in aligned_entities.items():
            # 计算基础融合分数
            base_score = self._calculate_base_fusion_score(entity_instances)
            
            # 计算一致性分数
            consistency_score = self._calculate_consistency_score(entity_instances)
            
            # 计算覆盖度分数
            coverage_score = self._calculate_coverage_score(entity_instances, len(aligned_entities))
            
            # 计算相关性分数
            relevance_score = await self._calculate_relevance_score(
                entity_instances[0], multimodal_query
            )
            
            # 综合分数计算
            final_score = (
                base_score * 0.4 +
                consistency_score * 0.3 +
                coverage_score * 0.2 +
                relevance_score * 0.1
            )
            
            fusion_result = FusionResult(
                entity_id=entity_id,
                entity_type=entity_instances[0]["entity_type"],
                entity_data=entity_instances[0]["entity_data"],
                confidence_score=final_score,
                source_queries=[str(inst["source_query_index"]) for inst in entity_instances],
                fusion_metadata={
                    "base_score": base_score,
                    "consistency_score": consistency_score,
                    "coverage_score": coverage_score,
                    "relevance_score": relevance_score,
                    "instance_count": len(entity_instances)
                }
            )
            
            fusion_results.append(fusion_result)
        
        return fusion_results
    
    def _calculate_base_fusion_score(self, entity_instances: List[Dict[str, Any]]) -> float:
        """计算基础融合分数"""
        
        if not entity_instances:
            return 0.0
        
        # 使用加权平均
        total_weight = 0
        weighted_sum = 0
        
        for instance in entity_instances:
            confidence = instance["original_confidence"]
            weight = 1.0  # 可以根据查询类型调整权重
            
            weighted_sum += confidence * weight
            total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _calculate_consistency_score(self, entity_instances: List[Dict[str, Any]]) -> float:
        """计算一致性分数"""
        
        if len(entity_instances) <= 1:
            return 1.0
        
        # 检查关键属性的一致性
        key_attributes = ["name", "type", "material", "category"]
        consistency_scores = []
        
        for attr in key_attributes:
            values = []
            for instance in entity_instances:
                entity_data = instance["entity_data"]
                if attr in entity_data and entity_data[attr] is not None:
                    values.append(str(entity_data[attr]).lower())
            
            if values:
                unique_values = set(values)
                consistency = 1.0 - (len(unique_values) - 1) / len(values)
                consistency_scores.append(consistency)
        
        return np.mean(consistency_scores) if consistency_scores else 0.5
    
    def _calculate_coverage_score(self, entity_instances: List[Dict[str, Any]], total_entities: int) -> float:
        """计算覆盖度分数"""
        
        # 基于实体在多个查询中出现的频率
        unique_queries = set(inst["source_query_index"] for inst in entity_instances)
        coverage = len(unique_queries) / max(1, len(entity_instances))
        
        # 归一化到0-1范围
        return min(1.0, coverage)
    
    async def _calculate_relevance_score(self, 
                                       entity_instance: Dict[str, Any], 
                                       multimodal_query: Optional[MultimodalQuery] = None) -> float:
        """计算相关性分数"""
        
        if not multimodal_query or not multimodal_query.has_text():
            return 0.5  # 默认相关性
        
        try:
            # 使用LLM评估相关性
            entity_data = entity_instance["entity_data"]
            relevance_score = await self._llm_evaluate_relevance(
                entity_data, multimodal_query.text_query
            )
            return relevance_score
            
        except Exception as e:
            logging.warning(f"相关性评估失败: {e}")
            return 0.5
    
    async def _llm_evaluate_relevance(self, entity_data: Dict[str, Any], text_query: str) -> float:
        """使用LLM评估实体与查询的相关性"""
        
        prompt = f"""
请评估以下CAD实体与用户查询的相关性，返回0-1之间的分数。

用户查询: {text_query}

实体信息:
- ID: {entity_data.get('id', 'unknown')}
- 名称: {entity_data.get('name', 'unknown')}
- 类型: {entity_data.get('type', 'unknown')}
- 材料: {entity_data.get('material', 'unknown')}
- 类别: {entity_data.get('category', 'unknown')}

请只返回一个0-1之间的数字，表示相关性分数。
"""
        
        try:
            response = await self.llm_manager.generate_response(
                prompt=prompt,
                model_name="gpt-3.5-turbo",
                temperature=0.1
            )
            
            # 解析分数
            score = float(response.strip())
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            logging.warning(f"LLM相关性评估失败: {e}")
            return 0.5
    
    async def _weighted_average_fusion(self, 
                                     fusion_results: List[FusionResult], 
                                     fusion_params: Dict[str, Any]) -> List[FusionResult]:
        """加权平均融合策略"""
        
        # 简单的加权平均，已在计算融合分数时完成
        return fusion_results
    
    async def _max_confidence_fusion(self, 
                                   fusion_results: List[FusionResult], 
                                   fusion_params: Dict[str, Any]) -> List[FusionResult]:
        """最大置信度融合策略"""
        
        # 为每个实体选择最高置信度的版本
        return fusion_results
    
    async def _consensus_based_fusion(self, 
                                    fusion_results: List[FusionResult], 
                                    fusion_params: Dict[str, Any]) -> List[FusionResult]:
        """基于共识的融合策略"""
        
        # 优先选择在多个查询中都出现的实体
        consensus_threshold = fusion_params.get("consensus_threshold", 2)
        
        consensus_results = []
        for result in fusion_results:
            if len(result.source_queries) >= consensus_threshold:
                # 提升共识实体的分数
                result.confidence_score *= 1.2
                consensus_results.append(result)
            elif result.confidence_score > 0.7:
                # 高置信度的单一结果也保留
                consensus_results.append(result)
        
        return consensus_results
    
    async def _llm_guided_fusion(self, 
                               fusion_results: List[FusionResult], 
                               fusion_params: Dict[str, Any]) -> List[FusionResult]:
        """LLM指导的融合策略"""
        
        # 使用LLM对结果进行智能筛选和排序
        try:
            llm_ranked_results = await self._llm_rank_results(fusion_results)
            return llm_ranked_results
        except Exception as e:
            logging.warning(f"LLM指导融合失败，回退到共识策略: {e}")
            return await self._consensus_based_fusion(fusion_results, fusion_params)
    
    async def _llm_rank_results(self, fusion_results: List[FusionResult]) -> List[FusionResult]:
        """使用LLM对结果进行排序"""
        
        # 构建LLM提示
        results_summary = []
        for i, result in enumerate(fusion_results[:10]):  # 限制数量避免提示过长
            summary = {
                "index": i,
                "id": result.entity_id,
                "type": result.entity_type,
                "name": result.entity_data.get("name", "unknown"),
                "confidence": result.confidence_score,
                "sources": len(result.source_queries)
            }
            results_summary.append(summary)
        
        prompt = f"""
请对以下CAD实体搜索结果进行智能排序，考虑相关性、置信度和数据质量。
返回排序后的索引列表，格式为JSON数组，例如: [2, 0, 1, 3, ...]

搜索结果:
{json.dumps(results_summary, ensure_ascii=False, indent=2)}

请只返回索引数组，不要其他内容。
"""
        
        try:
            response = await self.llm_manager.generate_response(
                prompt=prompt,
                model_name="gpt-3.5-turbo",
                temperature=0.1
            )
            
            # 解析排序索引
            ranked_indices = json.loads(response.strip())
            
            # 重新排序结果
            ranked_results = []
            for idx in ranked_indices:
                if 0 <= idx < len(fusion_results):
                    ranked_results.append(fusion_results[idx])
            
            # 添加未排序的结果
            used_indices = set(ranked_indices)
            for i, result in enumerate(fusion_results):
                if i not in used_indices:
                    ranked_results.append(result)
            
            return ranked_results
            
        except Exception as e:
            logging.warning(f"LLM排序失败: {e}")
            return fusion_results
    
    def _post_process_results(self, 
                            fusion_results: List[FusionResult], 
                            fusion_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """后处理结果"""
        
        confidence_threshold = fusion_params.get("confidence_threshold", 0.5)
        max_results = fusion_params.get("max_results", 20)
        
        # 过滤低置信度结果
        filtered_results = [
            result for result in fusion_results 
            if result.confidence_score >= confidence_threshold
        ]
        
        # 按置信度排序
        sorted_results = sorted(
            filtered_results, 
            key=lambda x: x.confidence_score, 
            reverse=True
        )
        
        # 限制结果数量
        limited_results = sorted_results[:max_results]
        
        # 转换为输出格式
        output_results = []
        for result in limited_results:
            output_result = {
                "id": result.entity_id,
                "type": result.entity_type,
                "data": result.entity_data,
                "confidence_score": result.confidence_score,
                "fusion_metadata": {
                    "source_queries": result.source_queries,
                    "fusion_details": result.fusion_metadata
                }
            }
            output_results.append(output_result)
        
        return output_results
    
    def _apply_diversity_balance(self, 
                               results: List[Dict[str, Any]], 
                               fusion_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用多样性和相关性平衡"""
        
        diversity_factor = fusion_params.get("diversity_factor", 0.3)
        
        if diversity_factor <= 0:
            return results
        
        # 简单的多样性策略：确保不同类型的实体都有代表
        type_counts = defaultdict(int)
        balanced_results = []
        
        for result in results:
            entity_type = result.get("type", "unknown")
            
            # 如果该类型的实体数量还不多，或者置信度很高，则添加
            if type_counts[entity_type] < 3 or result["confidence_score"] > 0.8:
                balanced_results.append(result)
                type_counts[entity_type] += 1
        
        return balanced_results
